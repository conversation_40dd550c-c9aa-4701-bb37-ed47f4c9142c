"""
简化的对话历史管理器（方案B：优化的单表方案）
负责对话历史的存储、查询和管理，按轮次存储对话数据
"""

import json
import logging
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from .dm_util import DMDatabase

logger = logging.getLogger(__name__)


class SimpleConversationManager:
    """简化的对话历史管理器（按轮次存储）"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化对话历史管理器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
    
    def save_conversation_round(
        self,
        thread_id: str,
        user_id: str,
        username: str,
        round_number: int,
        conversation_data: Dict[str, Any],
        processing_mode: str = None,
        primary_agent: str = None,
        status: str = "ACTIVE"
    ) -> bool:
        """
        保存单轮对话数据
        
        Args:
            thread_id: 对话线程ID
            user_id: 用户ID
            username: 用户名
            round_number: 轮次号
            conversation_data: 本轮对话数据
            processing_mode: 处理模式
            primary_agent: 主要agent
            status: 状态
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with DMDatabase(**self.db_config) as db:
                # 分析对话数据，提取统计信息
                stats = self._analyze_round_data(conversation_data)
                
                # 生成记录ID
                record_id = str(uuid.uuid4())
                
                # 检查是否已存在该轮次
                existing = db.execute_query(
                    "SELECT ID FROM SX_CONVERSATION_HISTORY WHERE THREAD_ID = :1 AND ROUND_NUMBER = :2 AND IS_DELETED = '0'",
                    (thread_id, round_number)
                )
                
                current_time = datetime.now()
                
                if existing:
                    # 更新现有轮次
                    update_sql = """
                    UPDATE SX_CONVERSATION_HISTORY SET
                        CONVERSATION_JSON = :1,
                        MESSAGE_COUNT = :2,
                        TOKEN_COUNT = :3,
                        USER_MSG_COUNT = :4,
                        AI_MSG_COUNT = :5,
                        TOOL_CALL_COUNT = :6,
                        PROCESSING_MODE = :7,
                        PRIMARY_AGENT = :8,
                        STATUS = :9,
                        UPDATED_AT = :10
                    WHERE THREAD_ID = :11 AND ROUND_NUMBER = :12 AND IS_DELETED = '0'
                    """
                    
                    params = (
                        json.dumps(conversation_data, ensure_ascii=False),
                        stats['message_count'],
                        stats['token_count'],
                        stats['user_msg_count'],
                        stats['ai_msg_count'],
                        stats['tool_call_count'],
                        processing_mode,
                        primary_agent,
                        status,
                        current_time,
                        thread_id,
                        round_number
                    )
                    
                    db.execute_update(update_sql, params)
                    logger.info(f"更新对话轮次成功: {thread_id}, 轮次: {round_number}")
                    
                else:
                    # 插入新轮次
                    insert_sql = """
                    INSERT INTO SX_CONVERSATION_HISTORY (
                        ID, THREAD_ID, USER_ID, USERNAME, ROUND_NUMBER,
                        CONVERSATION_JSON, MESSAGE_COUNT, TOKEN_COUNT,
                        USER_MSG_COUNT, AI_MSG_COUNT, TOOL_CALL_COUNT,
                        PROCESSING_MODE, PRIMARY_AGENT, STATUS,
                        CREATED_AT, UPDATED_AT, IS_DELETED
                    ) VALUES (
                        :1, :2, :3, :4, :5, :6, :7, :8, :9, :10, :11, :12, :13, :14, :15, :16, :17
                    )
                    """
                    
                    params = (
                        record_id,
                        thread_id,
                        user_id,
                        username,
                        round_number,
                        json.dumps(conversation_data, ensure_ascii=False),
                        stats['message_count'],
                        stats['token_count'],
                        stats['user_msg_count'],
                        stats['ai_msg_count'],
                        stats['tool_call_count'],
                        processing_mode,
                        primary_agent,
                        status,
                        current_time,
                        current_time,
                        '0'  # is_deleted
                    )
                    
                    db.execute_update(insert_sql, params)
                    logger.info(f"保存新对话轮次成功: {thread_id}, 轮次: {round_number}")
                
                db.commit()
                return True
                
        except Exception as e:
            logger.error(f"保存对话轮次失败: {thread_id}, 轮次: {round_number}, 错误: {e}")
            return False
    
    def get_conversation_history(
        self,
        thread_id: str,
        user_id: str = None,
        max_rounds: int = None,
        start_round: int = None
    ) -> Optional[Dict[str, Any]]:
        """
        获取指定对话的完整历史
        
        Args:
            thread_id: 对话线程ID
            user_id: 用户ID（可选，用于权限验证）
            max_rounds: 最大轮次数（可选）
            start_round: 起始轮次（可选）
            
        Returns:
            Dict: 合并后的完整对话历史数据，如果不存在返回None
        """
        try:
            with DMDatabase(**self.db_config) as db:
                sql = """
                SELECT ROUND_NUMBER, CONVERSATION_JSON, MESSAGE_COUNT, TOKEN_COUNT,
                       PROCESSING_MODE, PRIMARY_AGENT, STATUS, CREATED_AT
                FROM SX_CONVERSATION_HISTORY 
                WHERE THREAD_ID = :1 AND IS_DELETED = '0'
                """
                params = [thread_id]
                
                # 如果提供了user_id，添加用户权限验证
                if user_id:
                    sql += " AND USER_ID = :2"
                    params.append(user_id)
                
                # 添加轮次范围过滤
                if start_round is not None:
                    sql += f" AND ROUND_NUMBER >= :{len(params) + 1}"
                    params.append(start_round)
                
                sql += " ORDER BY ROUND_NUMBER"
                
                # 添加数量限制
                if max_rounds:
                    sql += f" LIMIT {max_rounds}"
                
                result = db.execute_query(sql, tuple(params))
                
                if not result:
                    return None
                
                # 合并所有轮次的消息
                all_messages = []
                total_tokens = 0
                processing_modes = set()
                primary_agents = set()
                first_created_at = None
                last_created_at = None
                
                for row in result:
                    round_data = json.loads(row['CONVERSATION_JSON'])
                    messages = round_data.get('messages', [])
                    all_messages.extend(messages)
                    
                    # 累计统计信息
                    total_tokens += row['TOKEN_COUNT'] or 0
                    if row['PROCESSING_MODE']:
                        processing_modes.add(row['PROCESSING_MODE'])
                    if row['PRIMARY_AGENT']:
                        primary_agents.add(row['PRIMARY_AGENT'])
                    
                    # 记录时间范围
                    if not first_created_at:
                        first_created_at = row['CREATED_AT']
                    last_created_at = row['CREATED_AT']
                
                # 构建完整的对话历史
                conversation_history = {
                    "thread_id": thread_id,
                    "messages": all_messages,
                    "total_count": len(all_messages),
                    "metadata": {
                        "thread_id": thread_id,
                        "total_rounds": len(result),
                        "total_messages": len(all_messages),
                        "total_tokens": total_tokens,
                        "processing_modes": list(processing_modes),
                        "primary_agents": list(primary_agents),
                        "status": "success",
                        "first_created_at": first_created_at.isoformat() if first_created_at else None,
                        "last_created_at": last_created_at.isoformat() if last_created_at else None
                    }
                }
                
                return conversation_history
                
        except Exception as e:
            logger.error(f"获取对话历史失败: {thread_id}, 错误: {e}")
            return None
    
    def list_user_conversations(
        self,
        user_id: str,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        获取用户的对话列表（按线程分组）
        
        Args:
            user_id: 用户ID
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            List[Dict]: 对话列表
        """
        try:
            with DMDatabase(**self.db_config) as db:
                sql = """
                SELECT THREAD_ID, 
                       MIN(CREATED_AT) as first_round_time, 
                       MAX(CREATED_AT) as last_round_time,
                       COUNT(*) as total_rounds, 
                       SUM(MESSAGE_COUNT) as total_messages, 
                       SUM(TOKEN_COUNT) as total_tokens,
                       MAX(PRIMARY_AGENT) as primary_agent,
                       MAX(PROCESSING_MODE) as processing_mode
                FROM SX_CONVERSATION_HISTORY 
                WHERE USER_ID = :1 AND IS_DELETED = '0'
                GROUP BY THREAD_ID
                ORDER BY last_round_time DESC
                LIMIT :2 OFFSET :3
                """
                
                result = db.execute_query(sql, (user_id, limit, offset))
                
                conversations = []
                for row in result:
                    # 获取第一轮的数据来生成标题
                    title = self._get_conversation_title(row['THREAD_ID'])
                    
                    conversations.append({
                        'thread_id': row['THREAD_ID'],
                        'title': title,
                        'total_rounds': row['total_rounds'],
                        'total_messages': row['total_messages'],
                        'total_tokens': row['total_tokens'],
                        'primary_agent': row['primary_agent'],
                        'processing_mode': row['processing_mode'],
                        'first_created_at': row['first_round_time'].isoformat() if row['first_round_time'] else None,
                        'last_created_at': row['last_round_time'].isoformat() if row['last_round_time'] else None
                    })
                
                return conversations
                
        except Exception as e:
            logger.error(f"获取用户对话列表失败: {user_id}, 错误: {e}")
            return []
    
    def delete_conversation(self, thread_id: str, user_id: str = None) -> bool:
        """
        软删除整个对话（所有轮次）
        
        Args:
            thread_id: 对话线程ID
            user_id: 用户ID（可选，用于权限验证）
            
        Returns:
            bool: 删除是否成功
        """
        try:
            with DMDatabase(**self.db_config) as db:
                sql = "UPDATE SX_CONVERSATION_HISTORY SET IS_DELETED = '1', UPDATED_AT = :1 WHERE THREAD_ID = :2"
                params = [datetime.now(), thread_id]
                
                if user_id:
                    sql += " AND USER_ID = :3"
                    params.append(user_id)
                
                affected_rows = db.execute_update(sql, tuple(params))
                db.commit()
                
                if affected_rows > 0:
                    logger.info(f"删除对话成功: {thread_id}, 影响轮次: {affected_rows}")
                    return True
                else:
                    logger.warning(f"对话不存在或无权限删除: {thread_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"删除对话失败: {thread_id}, 错误: {e}")
            return False
    
    def get_next_round_number(self, thread_id: str) -> int:
        """
        获取下一个轮次号
        
        Args:
            thread_id: 对话线程ID
            
        Returns:
            int: 下一个轮次号
        """
        try:
            with DMDatabase(**self.db_config) as db:
                sql = """
                SELECT MAX(ROUND_NUMBER) as max_round
                FROM SX_CONVERSATION_HISTORY 
                WHERE THREAD_ID = :1 AND IS_DELETED = '0'
                """
                
                result = db.execute_query(sql, (thread_id,))
                
                if result and result[0]['max_round'] is not None:
                    return result[0]['max_round'] + 1
                else:
                    return 1  # 第一轮
                    
        except Exception as e:
            logger.error(f"获取下一轮次号失败: {thread_id}, 错误: {e}")
            return 1  # 默认返回第一轮
    
    def _analyze_round_data(self, conversation_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析单轮对话数据，提取统计信息"""
        stats = {
            'message_count': 0,
            'token_count': 0,
            'user_msg_count': 0,
            'ai_msg_count': 0,
            'tool_call_count': 0
        }
        
        messages = conversation_data.get('messages', [])
        stats['message_count'] = len(messages)
        
        for msg in messages:
            if msg.get('role') == 'user':
                stats['user_msg_count'] += 1
            elif msg.get('role') == 'assistant':
                stats['ai_msg_count'] += 1
                
                # 统计tokens
                tokens = msg.get('tokens', {})
                if isinstance(tokens, dict):
                    stats['token_count'] += tokens.get('total_tokens', 0)
                
                # 统计工具调用
                for ai_item in msg.get('list', []):
                    if ai_item.get('type') == 'tool_call':
                        stats['tool_call_count'] += len(ai_item.get('tools', []))
        
        return stats
    
    def _get_conversation_title(self, thread_id: str) -> str:
        """获取对话标题（从第一轮的第一个用户消息）"""
        try:
            with DMDatabase(**self.db_config) as db:
                sql = """
                SELECT CONVERSATION_JSON
                FROM SX_CONVERSATION_HISTORY 
                WHERE THREAD_ID = :1 AND IS_DELETED = '0'
                ORDER BY ROUND_NUMBER
                LIMIT 1
                """
                
                result = db.execute_query(sql, (thread_id,))
                
                if result:
                    round_data = json.loads(result[0]['CONVERSATION_JSON'])
                    messages = round_data.get('messages', [])
                    
                    # 找到第一个用户消息
                    for msg in messages:
                        if msg.get('role') == 'user':
                            content = msg.get('content', '')
                            if content:
                                # 截取前50个字符作为标题
                                title = content.strip()[:50]
                                if len(content) > 50:
                                    title += '...'
                                return title
                
                # 如果没有找到用户消息，使用默认标题
                return f"对话 {thread_id}"
                
        except Exception as e:
            logger.error(f"获取对话标题失败: {thread_id}, 错误: {e}")
            return f"对话 {thread_id}"
