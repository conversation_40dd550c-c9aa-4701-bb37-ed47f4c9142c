"""
多智能体问答服务

集成统一的多智能体LangGraph系统，为API接口提供服务。
"""

from typing import Dict, Any, List, Optional, AsyncGenerator, Union
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langchain_openai import ChatOpenAI
from pydantic import SecretStr
import os
import sys
import json
import time
import asyncio
import re
from file_util import FILE_SERVER_URL

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.append(project_root)
from datetime import datetime, timedelta
from collections import defaultdict, deque
from uuid import uuid4

from rag_chat.logger import logger
from rag_chat.models import MultiAgentQARequest
from rag_chat.multi_agent import (
    create_unified_multi_agent_graph, 
    UnifiedQAState
)
from rag_chat.multi_agent.text_unified_graph import create_text_unified_multi_agent_graph
# 导入有界缓存管理器
from rag_chat.bounded_cache import get_resource_manager
# 导入修复的文本解析器
from rag_chat.multi_agent.text_parser import text_parser
# 导入长期记忆管理器
from rag_chat.long_term_memory import get_memory_manager, LongTermMemoryManager
# 导入简化的对话历史管理器
from rag_chat.simple_conversation_manager import SimpleConversationManager
try:
    from rag_chat.token_usage_tracker import global_token_tracker, reset_session_token_stats, get_session_token_stats
    logger.info("Token统计模块导入成功")
except ImportError as e:
    logger.error(f"Token统计模块导入失败: {e}")
    # 提供备用实现
    class DummyTracker:
        def get_stats(self): return {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}
        def reset_stats(self): pass

    # 只在导入失败时才创建DummyTracker
    global_token_tracker = DummyTracker()
    def reset_session_token_stats(): pass
    def get_session_token_stats(): return {"total_tokens": 0, "prompt_tokens": 0, "completion_tokens": 0}


class MultiAgentService:
    """多智能体问答服务"""
    
    def __init__(self):
        self.graph = None
        self.text_graph = None
        self.initialized = False
        # 使用有界缓存管理器替代无界字典和集合
        self.resource_manager = get_resource_manager()
        self.cite_counter = 0  # CITE引用计数器
        # 初始化长期记忆管理器
        self.long_term_memory = get_memory_manager()
        # 工具调用ID映射，用于关联tool_call和tool_result事件
        self.tool_call_id_map = {}  # key: 唯一标识符, value: 生成的tool_call_id
        # 工具调用计数器，用于生成唯一的序列号
        self.tool_call_counters = {}  # key: f"{agent_name}:{tool_name}", value: 递增计数器

        # 初始化对话历史管理器
        self.conversation_manager = None
        self._init_conversation_manager()

        # 当前对话数据收集器
        self.current_conversation_data = {}
        
    async def initialize(self, 
                        checkpointer: Any = None,
                        mcp_router: Any = None,
                        mcp_simple_router: Any = None):
        """初始化多智能体服务
        
        Args:
            checkpointer: 检查点保存器
            mcp_router: MCP RAG路由器
            mcp_simple_router: MCP简单路由器
        """
        try:
            logger.info("初始化多智能体问答服务")
            
            # 创建function calling模式的统一多智能体图
            # self.graph = create_unified_multi_agent_graph(
            #     checkpointer=checkpointer,
            #     mcp_router=mcp_router,
            #     mcp_simple_router=mcp_simple_router
            # )
            
            # 创建文本解析模式的统一多智能体图
            self.text_graph = create_text_unified_multi_agent_graph(
                checkpointer=checkpointer,
                mcp_router=mcp_router,
                mcp_simple_router=mcp_simple_router,
                execution_mode="react_mode",
                filter_agent_messages=True,
                enable_global_context_summarization=True,
                include_agent_name="inline"
            )
            
            # 确保Agent预缓存在服务启动时完成，避免第一次请求时的延迟
            # 获取图内部的AgentManager并等待其初始化完成
            if hasattr(self.text_graph, 'agent_manager'):
                agent_manager = self.text_graph.agent_manager
                if not agent_manager.initialized:
                    logger.info("开始等待Agent预缓存初始化...")
                    await agent_manager.initialize()
                    logger.info("Agent预缓存初始化完成")
            
            self.initialized = True
            logger.info("多智能体问答服务初始化完成（支持function calling、text parsing和react模式，Agent预缓存已就绪）")

        except Exception as e:
            logger.error(f"智能体问答服务初始化失败: {e}")
            self.initialized = False
            raise
    
    async def process_question(
        self,
        request: MultiAgentQARequest,
        userid: str = "",
        apikey: str = "",
        qa_id: str = "",
        extra_headers: Optional[Dict[str, Any]] = None,
        show_thinking: bool = False,
        execution_mode: str = "react_mode",
        langfuse_handler: Optional[Any] = None
    ) -> AsyncGenerator[str, None]:
        """处理用户问题并流式返回结果
        
        Args:
            request: 问答请求
            userid: 用户ID
            apikey: API密钥
            qa_id: 问答ID
            extra_headers: 额外请求头
            show_thinking: 是否显示思考内容
            execution_mode: 执行模式 ("function_call" 或 "text_parsing" 或 "react_mode")
            
        Yields:
            流式响应数据
        """
        if not self.initialized:
            yield self._format_sse_data("error", "多智能体服务未初始化")
            return
        
        # 验证执行模式
        if execution_mode not in ["function_call", "text_parsing", "react_mode"]:
            yield self._format_sse_data("error", f"不支持的执行模式: {execution_mode}")
            return
        
        try:
            # 重置本次会话的token统计 - 移到处理开始前
            logger.info("重置本次会话的token统计")
            reset_session_token_stats()
            
            # 清理消息缓冲区和状态，防止历史消息泄露
            self.resource_manager.clear_all()
            
            # 清理工具调用ID映射表，防止内存泄漏
            self._cleanup_tool_call_mappings()
            
            # 设置当前用户上下文（用于工具调用）
            try:
                from rag_chat.context_utils import set_current_user_context
                set_current_user_context(userid=userid, qa_id=qa_id)
                logger.debug(f"已设置用户上下文: userid={userid}, qa_id={qa_id}")
            except ImportError:
                logger.warning("无法导入context_utils，长期记忆工具可能无法正常工作")
            
            # 生成qa_id
            if not qa_id:
                qa_id = str(uuid4())

            # 生成或使用thread_id
            thread_id = request.thread_id or f"{userid}--{qa_id}"

            # 初始化当前对话数据收集器
            self._init_conversation_data(thread_id, userid, request.question, qa_id)

            # 确定模型描述
            model_desc = self._get_model_desc(request.model)

            logger.info(f"开始处理多智能体问答: {request.question[:50]}...")
            
            # 1. 检索相关长期记忆增强上下文
            relevant_memories = []
            if self.long_term_memory.config.enabled and userid and extra_headers:
                # 从请求头获取API密钥和用户名
                api_key = extra_headers.get("X-API-Key", "") or apikey
                username = extra_headers.get("X-Username", "") or userid
                
                if api_key and username:
                    try:
                        relevant_memories = await self.long_term_memory.search_memories(
                            query=request.question,
                            userid=userid,
                            api_key=api_key,
                            username=username,
                            qa_id=qa_id,
                            limit=self.long_term_memory.config.search_limit,
                            threshold=self.long_term_memory.config.search_threshold
                        )
                        if relevant_memories:
                            logger.info(f"检索到 {len(relevant_memories)} 条相关长期记忆")
                            # 发送记忆检索状态
                            yield self._format_sse_data("memory_retrieved", {
                                "count": len(relevant_memories),
                                "memories": [{"memory": m.memory, "score": m.score} for m in relevant_memories[:3]]  # 只显示前3条
                            })
                    except Exception as e:
                        logger.warning(f"检索长期记忆失败: {e}")
                else:
                    logger.warning("缺少API密钥或用户名，跳过长期记忆检索")
            
            # 构建消息内容，支持图片和长期记忆
            human_message = await self._build_human_message(request, relevant_memories)
            
            # 根据执行模式选择合适的状态类
            if execution_mode in ["text_parsing", "react_mode"]:
                from multi_agent.text_unified_graph import TextUnifiedQAState
                
                current_question = request.question

                initial_state = TextUnifiedQAState(
                    messages=[human_message],
                    mcp_ids=list(request.mcp_ids) if request.mcp_ids else [],
                    extral_rag_ids=list(request.extral_rag_ids) if request.extral_rag_ids else [],
                    file_uploads=getattr(request, 'uploaded_files', []),
                    model_desc=model_desc,
                    thread_id=f"{userid}--{qa_id}",
                    extra_headers=extra_headers or {},
                    execution_mode=execution_mode,
                    current_question=current_question
                )
            else:
                initial_state = UnifiedQAState(
                    messages=[human_message],
                    mcp_ids=list(request.mcp_ids) if request.mcp_ids else [],
                    extral_rag_ids=list(request.extral_rag_ids) if request.extral_rag_ids else [],
                    file_uploads=getattr(request, 'uploaded_files', []),
                    model_desc=model_desc,
                    thread_id=f"{userid}--{qa_id}",
                    extra_headers=extra_headers or {}
                )
            
            # 构建配置
            config = {
                "configurable": {
                    "thread_id": initial_state.thread_id
                }
            }

            # 如果提供了langfuse_handler，则添加到配置中
            if langfuse_handler:
                config["callbacks"] = [langfuse_handler]
            
            # 发送开始状态
            yield self._format_sse_data("status", {"message": "开始处理多智能体问答"})
            
            # 根据执行模式选择合适的图
            if execution_mode in ["text_parsing", "react_mode"]:
                selected_graph = self.text_graph
                logger.info("使用文本解析模式执行多智能体问答")
            else:
                selected_graph = self.graph if self.graph else self.text_graph
                logger.info("使用function calling模式执行多智能体问答")
            
            # 检测是否为custom_rag请求
            is_custom_rag_request = bool(request.extral_rag_ids) if hasattr(request, 'extral_rag_ids') else False
            if is_custom_rag_request:
                logger.info(f"检测到custom_rag请求，extral_rag_ids: {request.extral_rag_ids}")
            
            # 使用messages流式模式以获得更细粒度的流式输出
            agent_message_started = {}
            displayed_tool_calls = set()
            processing_stats = {
                "agents_used": set(),
                "tools_called": set(),
                "custom_rag_used": False
            }

            """
            subgraph=fasle
            (
                'messages', 
                (
                    HumanMessage(content=[{'type': 'text', 'text': '帮我查一下三峡的主要业务。'}], additional_kwargs={}, response_metadata={}, id='b4019d38-dda2-48fd-96b0-71e4832621ac'), 
                    {'thread_id': 'jiliang--unnk', 'langgraph_step': 70, 'langgraph_node': 'mode_switcher', 'langgraph_triggers': ('branch:to:mode_switcher',), 'langgraph_path': ('__pregel_pull', 'mode_switcher'), 'langgraph_checkpoint_ns': 'mode_switcher:a08efbf2-dd85-d325-7bff-76234acff607'}
                )
            )

            subgraph=true
            (
                (), 
                'messages', 
                (
                    HumanMessage(content=[{'type': 'text', 'text': '帮我查一下三峡的主要业务。'}], additional_kwargs={}, response_metadata={}, id='b4019d38-dda2-48fd-96b0-71e4832621ac'), 
                    {'thread_id': 'jiliang--unnk', 'langgraph_step': 74, 'langgraph_node': 'mode_switcher', 'langgraph_triggers': ('branch:to:mode_switcher',), 'langgraph_path': ('__pregel_pull', 'mode_switcher'), 'langgraph_checkpoint_ns': 'mode_switcher:7e055a08-0882-1fa4-fc16-52c4e28d24d3'}
                )
            )
            """
                
            # 使用单一stream_mode减少重复问题
            # stream_mode=["messages", "custom"] 会导致重复输出，改为只使用messages
            async for chunk in selected_graph.astream(
                initial_state, 
                config=config, 
                stream_mode="messages",  # 只使用messages模式，避免重复
                subgraphs=False
            ):  
                # 由于只使用"messages"模式，chunk格式简化为 (message_chunk, metadata)
                if isinstance(chunk, tuple) and len(chunk) == 2:
                    # 直接处理消息流式输出 
                    for sse_data in self._process_message_chunk(
                        ("messages", chunk), agent_message_started, displayed_tool_calls, 
                        show_thinking, processing_stats, is_custom_rag_request
                    ):
                        yield sse_data
            
            # 等待所有回调完成，然后获取token使用统计
            await asyncio.sleep(1e-6)  # 短暂延迟确保所有回调完成
            token_stats = get_session_token_stats()
            logger.info(f"最终token统计: {token_stats}")
            print(f"最终token统计: {token_stats}")
            
            # 2. 启动后台异步记忆存储任务
            if self.long_term_memory.config.enabled and userid and extra_headers:
                # 从请求头获取API密钥和用户名（用于后台存储）
                api_key = extra_headers.get("X-API-Key", "")
                username = extra_headers.get("X-Username", "")
                
                if api_key and username:
                    try:
                        # 获取最终的AI响应
                        final_ai_message = await self._extract_final_response(selected_graph, config)
                        
                        if final_ai_message:
                            # 启动后台任务存储记忆（不阻塞响应）
                            asyncio.create_task(self._store_memory_background(
                                userid=userid,
                                qa_id=qa_id,
                                user_question=request.question,
                                ai_response=final_ai_message,
                                request=request,
                                api_key=api_key,
                                username=username
                            ))
                            logger.info("已启动后台记忆存储任务")
                            yield self._format_sse_data("memory_storage_started", {"message": "开始后台存储对话记忆"})
                    except Exception as e:
                        logger.warning(f"启动后台记忆存储失败: {e}")
                else:
                    logger.warning("缺少API密钥或用户名，跳过后台记忆存储")

            # 发送完成状态
            completion_data = {
                "message": "多智能体问答处理完成",
                "execution_mode": execution_mode,
                "agents_used": list(processing_stats["agents_used"]),
                "tools_called": list(processing_stats["tools_called"]),
                "custom_rag_used": processing_stats["custom_rag_used"],
                "token_usage": token_stats
            }
            yield self._format_sse_data("completion", completion_data)
            logger.info(f"多智能体问答处理完成，使用了Agents: {list(processing_stats['agents_used'])}, "
                       f"Token消耗: {token_stats.get('total_tokens', 0)}")

            # 保存对话历史到数据库
            await self._save_conversation_history_async(
                thread_id=thread_id,
                user_id=userid,
                username=extra_headers.get("X-Username", userid) if extra_headers else userid,
                processing_mode=execution_mode,
                primary_agent=list(processing_stats["agents_used"])[0] if processing_stats["agents_used"] else "unknown",
                token_stats=token_stats
            )

        except Exception as e:
            logger.error(f"多智能体问答处理失败: {e}")
            yield self._format_sse_data("error", str(e))
        finally:
            # 清除用户上下文
            try:
                from rag_chat.context_utils import clear_current_user_context
                clear_current_user_context()
                logger.debug("已清除用户上下文")
            except ImportError:
                pass
    
    def _process_message_chunk(self, chunk, agent_message_started: Dict[str, bool], displayed_tool_calls: set, show_thinking: bool = False, processing_stats: Dict[str, Any] = None, is_custom_rag_request: bool = False):
        """处理消息chunk并生成SSE数据
        
        Args:
            chunk: 原始的消息chunk，格式为 (namespace, (message_chunk, metadata))
            agent_message_started: 跟踪已开始的agent消息
            displayed_tool_calls: 跟踪已显示的工具调用
            show_thinking: 是否显示思考内容
            processing_stats: 处理统计信息
            is_custom_rag_request: 是否为自定义RAG请求
            
        Yields:
            格式化的SSE数据
        """
        if processing_stats is None:
            processing_stats = {}
        
        # 获取有界去重集合
        processed_messages = self.resource_manager.get_processed_messages()
        
        if isinstance(chunk, tuple) and len(chunk) == 2:
            stream_type, message_data = chunk
            namespace = ""
        elif isinstance(chunk, tuple) and len(chunk) == 3:
            namespace, stream_type, message_data = chunk
        else:
            logger.warning(f"不支持的消息chunk格式: {chunk}")
            return
            
        # 只处理messages类型的stream
        if stream_type == 'messages' and isinstance(message_data, tuple) and len(message_data) == 2:
            message_chunk, metadata = message_data
            
            if isinstance(message_chunk, AIMessage):
                # 获取节点名称和步骤，用于过滤系统节点和去重
                current_node = metadata.get("langgraph_node", "unknown")
                current_step = metadata.get("langgraph_step", 0)
                
                # 检测重复消息：基于消息ID和步骤进行去重
                message_id = getattr(message_chunk, 'id', None)
                is_finish_reason_stop = (hasattr(message_chunk, 'response_metadata') and 
                                       message_chunk.response_metadata.get('finish_reason') == 'stop')
                
                # 获取LangGraph内部信息用于高级去重
                langgraph_path = metadata.get("langgraph_path", ())
                is_pregel_pull = "__pregel_pull" in langgraph_path if langgraph_path else False
                
                # 多层去重策略
                # 策略1: 基于__pregel_pull路径的状态同步消息过滤（最直接有效）
                if is_pregel_pull and message_chunk.content and len(message_chunk.content) > 50:
                    logger.debug(f"跳过LangGraph状态同步重复消息: 节点={current_node}, 消息ID={message_id}")
                    return  # 直接跳过状态同步导致的重复完整消息
                
                # 策略2: 基于消息ID+finish_reason的完整消息去重
                if message_id and is_finish_reason_stop:
                    # 对于有finish_reason=stop的完整消息，使用消息ID作为去重标识
                    dedup_key = f"{message_id}_complete"
                    
                    if dedup_key in processed_messages:
                        logger.debug(f"跳过重复的完整消息: 节点={current_node}, 消息ID={message_id}")
                        return  # 跳过重复的完整消息
                    
                    # 标记这个完整消息已处理
                    processed_messages.add(dedup_key)
                
                # 详细日志记录消息信息
                # logger.debug(f"处理AIMessage: 节点={current_node}, 步骤={current_step}, "
                #            f"有finish_reason={is_finish_reason_stop}, "
                #            f"内容长度={len(message_chunk.content) if message_chunk.content else 0}, "
                #            f"消息ID={message_id}, "
                #            f"内容预览={message_chunk.content[:50] if message_chunk.content else 'None'}...")
                
                # 过滤系统内部节点，这些节点不应该有用户可见的内容输出
                system_nodes_to_filter = {
                    "mode_switcher",
                    "message_cleaner", 
                    "system_mode_switcher",
                    "system_message_cleaner"
                }
                
                if current_node in system_nodes_to_filter:
                    return  # 跳过系统节点的内容输出
                
                # 解析agent名称
                agent_name = "agent"  # 默认值
                
                # 方式1: 优先从metadata中的langgraph_node获取，确保准确性
                if current_node != "unknown":
                    # 映射特定的node名称到友好的agent名称
                    node_to_agent_mapping = {
                        "text_multimodal_agent": "multimodal_agent",
                        "text_knowledge_agent": "knowledge_agent", 
                        "text_mcp_agent": "mcp_agent",
                        "text_simple_chat": "simple_chat_agent",
                        "text_custom_rag_agent": "custom_rag_agent",
                        "multimodal_agent": "multimodal_agent",
                        "knowledge_agent": "knowledge_agent",
                        "mcp_agent": "mcp_agent", 
                        "simple_chat": "simple_chat_agent",
                        "custom_rag_agent": "custom_rag_agent",
                        "intent_analyzer": "intent_analyzer",
                        "_intent_analyzer_node": "intent_analyzer",
                        "mode_switcher": "system_mode_switcher",
                        "message_cleaner": "system_message_cleaner",
                        "global_context_summarization": "system_context_summarizer"
                    }
                    agent_name = node_to_agent_mapping.get(current_node, current_node)
                
                # 方式2: 从message.name获取（仅作为备选）
                elif hasattr(message_chunk, 'name') and message_chunk.name:
                    agent_name = message_chunk.name
                
                # 方式3: 从namespace解析
                elif namespace and len(namespace) > 0:
                    namespace_part = namespace[0].split(':')[0]
                    if namespace_part and namespace_part != "main":
                        agent_name = namespace_part
                
                # 更新统计信息
                if "agents_used" in processing_stats:
                    processing_stats["agents_used"].add(agent_name)
                
                # 处理reasoning_content（DeepSeek标准模式）和常规内容
                reasoning_content = None
                if hasattr(message_chunk, 'additional_kwargs') and message_chunk.additional_kwargs:
                    reasoning_content = message_chunk.additional_kwargs.get('reasoning_content')
                
                # 确定要处理的内容和是否为思考模式
                if reasoning_content and show_thinking:
                    # DeepSeek reasoning_content：作为思考内容流式输出
                    content_to_process = str(reasoning_content)
                    is_thinking = True
                    model_name = self._extract_model_name(metadata, message_chunk)
                    agent_name_str = str(agent_name) if agent_name else "unknown"
                    
                    # 为 reasoning_content 设置思考开始状态
                    reasoning_message_buffer = self.resource_manager.get_message_buffer()
                    reasoning_thinking_state_key = f"{agent_name_str}:thinking_state"
                    if reasoning_message_buffer.get(reasoning_thinking_state_key, "none") == "none":
                        reasoning_message_buffer.set(reasoning_thinking_state_key, "started")
                    
                    # 流式输出思考内容
                    for sse_data in self._process_and_buffer_text_stream(
                        content_to_process, agent_name_str, namespace, 
                        agent_message_started, is_thinking, processing_stats, is_custom_rag_request, model_name
                    ):
                        yield sse_data
                    
                    # reasoning_content通常没有</think>标签，只更新状态不发送thinking_complete
                    reasoning_message_buffer = self.resource_manager.get_message_buffer()
                    reasoning_thinking_state_key = f"{agent_name_str}:thinking_state"
                    reasoning_thinking_state = reasoning_message_buffer.get(reasoning_thinking_state_key, "none")
                    if reasoning_thinking_state == "started":
                        reasoning_message_buffer.set(reasoning_thinking_state_key, "ended")
                        # 移除thinking_complete发送
                        
                    # 如果还有常规内容，作为正常输出处理
                    if message_chunk.content:
                        content_str = str(message_chunk.content) if isinstance(message_chunk.content, str) else str(message_chunk.content)
                        for sse_data in self._process_and_buffer_text_stream(
                            content_str, agent_name_str, namespace, 
                            agent_message_started, False, processing_stats, is_custom_rag_request, model_name
                        ):
                            yield sse_data
                            
                elif message_chunk.content:
                    # 常规内容处理
                    is_thinking = self._is_thinking_content(message_chunk, metadata)
                    
                    # 如果是thinking内容且不显示thinking，则跳过
                    if is_thinking and not show_thinking:
                        pass  # 跳过思考内容
                    else:
                        # 使用带缓冲区的解析器处理流式文本
                        model_name = self._extract_model_name(metadata, message_chunk)
                        agent_name_str = str(agent_name) if agent_name else "unknown"
                        # 处理多模态内容：转换为字符串
                        if isinstance(message_chunk.content, list):
                            # 多模态内容，提取文本部分
                            content_str = ""
                            for item in message_chunk.content:
                                if isinstance(item, dict) and item.get("type") == "text":
                                    content_str += str(item.get("text", ""))
                        else:
                            content_str = str(message_chunk.content)
                            
                        for sse_data in self._process_and_buffer_text_stream(
                            content_str, agent_name_str, namespace, 
                            agent_message_started, is_thinking, processing_stats, is_custom_rag_request, model_name
                        ):
                            yield sse_data
                
                # 处理function calling模式的工具调用
                if hasattr(message_chunk, 'tool_calls') and message_chunk.tool_calls:
                    for tool_call in message_chunk.tool_calls:
                        tool_name = self._extract_tool_name(tool_call)
                        tool_id = self._extract_tool_id(tool_call)
                        
                        if tool_name and tool_name.strip() and tool_name != "None":
                            unique_call_id = f"{agent_name}:{tool_name}:{tool_id}" if tool_id else f"{agent_name}:{tool_name}:{len(displayed_tool_calls)}"
                            
                            if unique_call_id not in displayed_tool_calls:
                                displayed_tool_calls.add(unique_call_id)
                                # 更新统计信息
                                if "tools_called" in processing_stats:
                                    processing_stats["tools_called"].add(tool_name)
                                
                                # 生成统一的工具调用ID
                                tool_call_id = self._generate_tool_call_id(agent_name, tool_name, tool_id)
                                
                                # 生成唯一映射key并建立映射关系
                                unique_mapping_key = self._generate_unique_mapping_key(agent_name, tool_name, tool_id)
                                self.tool_call_id_map[unique_mapping_key] = tool_call_id
                                
                                # 调试信息：输出映射建立的详细信息（流式输出路径1）
                                logger.info(f"🔧 Tool Call Mapping Created (Stream Path 1) - Agent: {agent_name}, Tool: {tool_name}")
                                logger.info(f"   Generated tool_call_id: {tool_call_id}")
                                logger.info(f"   Original tool_id: {tool_id}")
                                logger.info(f"   Mapping key: {unique_mapping_key}")
                                logger.info(f"   Mapping table size: {len(self.tool_call_id_map)}")
                                
                                yield self._format_sse_data("tool_call", {
                                    "agent_name": agent_name,
                                    "tool_name": tool_name,
                                    "tool_id": tool_id,
                                    "tool_call_id": tool_call_id
                                })
                
                # 处理additional_kwargs中的工具调用
                if hasattr(message_chunk, 'additional_kwargs') and message_chunk.additional_kwargs:
                    if 'tool_calls' in message_chunk.additional_kwargs:
                        for tool_call in message_chunk.additional_kwargs['tool_calls']:
                            tool_name = self._extract_tool_name(tool_call)
                            tool_id = self._extract_tool_id(tool_call)
                            
                            if tool_name and tool_name.strip() and tool_name != "None":
                                unique_call_id = f"{agent_name}:{tool_name}:{tool_id}" if tool_id else f"{agent_name}:{tool_name}:{len(displayed_tool_calls)}"
                                
                                if unique_call_id not in displayed_tool_calls:
                                    displayed_tool_calls.add(unique_call_id)
                                    # 更新统计信息
                                    if "tools_called" in processing_stats:
                                        processing_stats["tools_called"].add(tool_name)
                                    
                                    # 生成统一的工具调用ID
                                    tool_call_id = self._generate_tool_call_id(agent_name, tool_name, tool_id)
                                    
                                    # 生成唯一映射key并建立映射关系
                                    unique_mapping_key = self._generate_unique_mapping_key(agent_name, tool_name, tool_id)
                                    self.tool_call_id_map[unique_mapping_key] = tool_call_id
                                    
                                    # 调试信息：输出映射建立的详细信息（流式输出路径2）
                                    logger.info(f"🔧 Tool Call Mapping Created (Stream Path 2) - Agent: {agent_name}, Tool: {tool_name}")
                                    logger.info(f"   Generated tool_call_id: {tool_call_id}")
                                    logger.info(f"   Original tool_id: {tool_id}")
                                    logger.info(f"   Mapping key: {unique_mapping_key}")
                                    logger.info(f"   Mapping table size: {len(self.tool_call_id_map)}")
                                    
                                    yield self._format_sse_data("tool_call", {
                                        "agent_name": agent_name,
                                        "tool_name": tool_name,
                                        "tool_id": tool_id,
                                        "tool_call_id": tool_call_id
                                    })
                
                # 检查消息是否完成
                if hasattr(message_chunk, 'response_metadata') and message_chunk.response_metadata.get('finish_reason') == 'stop':
                    logger.debug(f"检测到消息完成: 节点={current_node}, 步骤={current_step}, 内容长度={len(message_chunk.content) if message_chunk.content else 0}")
                    
                    # 消息结束，清空并处理缓冲区剩余内容
                    output_key = f"{agent_name}:output"
                    thinking_key = f"{agent_name}:thinking"
                    
                    # 获取消息缓冲区
                    message_buffer = self.resource_manager.get_message_buffer()
                    
                    for key in [output_key, thinking_key]:
                        remaining_content = message_buffer.pop(key)
                        if remaining_content:
                            is_thinking_key = key.endswith(":thinking")
                            
                            # 特殊处理：intent_analyzer 的完整结果格式化输出
                            if agent_name == "intent_analyzer" and not is_thinking_key:
                                yield from self._format_intent_analyzer_result(remaining_content, agent_name, namespace)
                            # 特殊处理：上下文总结节点的固定信息输出
                            elif agent_name == "system_context_summarizer" and not is_thinking_key:
                                yield from self._format_context_summarizer_result(agent_name, namespace)
                            else:
                                # 对于其他agent（如simple_chat_agent等），绝对不重复输出剩余内容
                                # 因为内容已经在流式过程中输出了，重复输出会导致用户看到重复的完整回答
                                logger.debug(f"Agent {agent_name} 消息完成，跳过缓冲区剩余内容输出，长度: {len(remaining_content)}")
                                

                    # 重置该agent的消息状态
                    if output_key in agent_message_started:
                        agent_message_started.pop(output_key)
                        yield self._format_sse_data("agent_complete", {
                            "agent_name": agent_name,
                            "namespace": namespace[0] if namespace else "main"
                        })
                    
                    # 新的思考状态管理：在agent消息完成时只更新状态，不发送thinking_complete
                    if thinking_key in agent_message_started:
                        agent_message_started.pop(thinking_key)
                        
                        # 检查新的思考状态管理系统
                        message_buffer = self.resource_manager.get_message_buffer()
                        thinking_state_key = f"{agent_name}:thinking_state"
                        current_thinking_state = message_buffer.get(thinking_state_key, "none")
                        
                        # 只更新状态，移除thinking_complete发送
                        if current_thinking_state == "started":
                            message_buffer.set(thinking_state_key, "ended")
                            # 移除thinking_complete发送
            
            # 处理工具调用结果消息（ToolMessage）
            elif hasattr(message_chunk, 'tool_call_id') or \
            (str(message_chunk.additional_kwargs.get('is_tool_result', '')).lower() == 'true' 
                and "[HANDOFF]" not in getattr(message_chunk, 'content', '')
                and message_chunk.additional_kwargs.get("message_type", '') != "agent_status_report"):  # ToolMessage
                # 解析agent名称
                if namespace and len(namespace) > 0:
                    agent_name = namespace[0].split(':')[0]
                else:
                    agent_name = metadata.get("langgraph_node", "main")
                
                original_tool_call_id = getattr(message_chunk, 'tool_call_id', 'unknown')
                tool_name = getattr(message_chunk, 'name', 'unknown_tool')
                content = getattr(message_chunk, 'content', '')
                
                # 检查是否有工具执行信息
                additional_kwargs = getattr(message_chunk, 'additional_kwargs', {})
                is_tool_result = additional_kwargs.get('is_tool_result', False)
                tool_execution_info = additional_kwargs.get('tool_execution_info', {})
                
                # 如果有详细的工具执行信息，使用它；否则使用默认映射
                if tool_execution_info:
                    agent_name = tool_execution_info.get('agent_name', agent_name)
                    tool_name = tool_execution_info.get('tool_name', tool_name)
                    friendly_name = tool_execution_info.get('display_name', tool_name)
                    timestamp = tool_execution_info.get('timestamp', metadata.get("timestamp"))
                    content_length = tool_execution_info.get('content_length', len(content))
                else:
                    timestamp = metadata.get("timestamp")
                    content_length = len(content)
                    friendly_name = tool_name
                
                # 智能查找对应的tool_call_id（支持多种映射key）
                tool_call_id = self._find_matching_tool_call_id(agent_name, tool_name, original_tool_call_id)
                
                # 调试信息：输出映射查找的详细信息
                logger.info(f"🔍 Tool Result Mapping Debug - Agent: {agent_name}, Tool: {tool_name}")
                logger.info(f"   Original tool_call_id from ToolMessage: {original_tool_call_id}")
                logger.info(f"   Found tool_call_id from mapping: {tool_call_id}")
                logger.info(f"   Current mapping table: {dict(list(self.tool_call_id_map.items())[:3])}...")  # 只显示前3个
                logger.info(f"   Current counters: {self.tool_call_counters}")
                
                # 检查是否为错误结果
                is_error = tool_execution_info.get('is_error', False) if tool_execution_info else False
                
                # 返回完整的工具结果信息
                tool_result_data = {
                    "agent_name": agent_name,
                    "tool_call_id": tool_call_id,
                    "tool_name": tool_name,
                    "display_name": friendly_name,
                    "content": content,
                    "truncated_content": content[:500] + "..." if len(content) > 500 else content,
                    "content_length": content_length,
                    "timestamp": timestamp,
                    "is_compatibility_mode": is_tool_result,
                    "is_error": is_error
                }
                
                # 添加可选字段
                if tool_execution_info and tool_execution_info.get('search_query'):
                    tool_result_data["search_query"] = tool_execution_info.get('search_query')
                
                yield self._format_sse_data("tool_result", tool_result_data)
    
    def _process_tool_execution_event(self, event_data: dict, namespace):
        """处理自定义工具执行事件"""
        event = event_data.get("event", "unknown")
        agent_name = event_data.get("agent_name", "agent")
        tool_name = event_data.get("tool_name", "unknown_tool")
        display_name = event_data.get("display_name", tool_name)
            
        if event == "tool_result":
            # 工具执行完成
            content = event_data.get("content", "")
            
            # 智能查找对应的tool_call_id
            original_tool_call_id = event_data.get("tool_call_id")
            tool_call_id = original_tool_call_id or self._find_matching_tool_call_id(agent_name, tool_name, original_tool_call_id)
            
            yield self._format_sse_data("tool_result", {
                "agent_name": agent_name,
                "tool_name": tool_name,
                "display_name": display_name,
                "content": content,
                "truncated_content": event_data.get("truncated_content", content[:500] + "..." if len(content) > 500 else content),
                "content_length": event_data.get("content_length", len(content)),
                "tool_call_id": tool_call_id,
                "timestamp": event_data.get("timestamp"),
                "is_error": False
            })
            
        elif event == "tool_error":
            # 工具执行错误
            content = event_data.get("content", "工具执行失败")
            error = event_data.get("error", "未知错误")
            
            # 智能查找对应的tool_call_id
            original_tool_call_id = event_data.get("tool_call_id")
            tool_call_id = original_tool_call_id or self._find_matching_tool_call_id(agent_name, tool_name, original_tool_call_id)
            
            yield self._format_sse_data("tool_result", {
                "agent_name": agent_name,
                "tool_name": tool_name,
                "display_name": display_name,
                "content": content,
                "truncated_content": content[:500] + "..." if len(content) > 500 else content,
                "content_length": event_data.get("content_length", len(content)),
                "tool_call_id": tool_call_id,
                "is_error": True,
                "timestamp": event_data.get("timestamp"),
                "error": error
            })
        
        else:
            logger.warning(f"未知的工具执行事件: {event}")

    def _extract_tool_name(self, tool_call) -> str:
        """提取工具名称"""
        if isinstance(tool_call, dict):
            return tool_call.get('name', tool_call.get('function', {}).get('name', 'unknown'))
        elif hasattr(tool_call, 'name'):
            return tool_call.name
        elif hasattr(tool_call, 'function') and hasattr(tool_call.function, 'name'):
            return tool_call.function.name
        return 'unknown'
    
    def _extract_tool_id(self, tool_call) -> Optional[str]:
        """提取工具ID"""
        if isinstance(tool_call, dict):
            return tool_call.get('id')
        elif hasattr(tool_call, 'id'):
            return tool_call.id
        return None
    
    def _generate_unique_mapping_key(self, agent_name: str, tool_name: str, tool_id: Optional[str] = None) -> str:
        """生成唯一的映射key，确保多次调用同一工具时不冲突"""
        # 方案1：如果有原始tool_id，直接使用它（LangChain生成的ID本身就是唯一的）
        if tool_id and tool_id != "unknown":
            return f"toolid_{tool_id}"
        
        # 方案2：没有原始tool_id时，使用计数器生成唯一key
        counter_key = f"{agent_name}:{tool_name}"
        if counter_key not in self.tool_call_counters:
            self.tool_call_counters[counter_key] = 0
        
        self.tool_call_counters[counter_key] += 1
        sequence_num = self.tool_call_counters[counter_key]
        
        return f"seq_{agent_name}:{tool_name}:{sequence_num}"
    
    def _generate_tool_call_id(self, agent_name: str, tool_name: str, tool_id: Optional[str] = None) -> str:
        """生成统一的工具调用ID"""
        import uuid
        import time
        
        # 优先使用原始tool_id，否则生成新的唯一ID
        if tool_id and tool_id != "unknown":
            base_id = tool_id
        else:
            timestamp = int(time.time() * 1000)  # 毫秒时间戳
            unique_suffix = str(uuid.uuid4())[:8]  # UUID前8位
            base_id = f"call_{tool_name}_{timestamp}_{unique_suffix}"
            
        return base_id
    
    def _find_matching_tool_call_id(self, agent_name: str, tool_name: str, original_tool_call_id: Optional[str] = None) -> str:
        """智能查找匹配的tool_call_id，支持多种映射策略"""
        
        # 策略1：如果有原始tool_call_id，尝试用toolid_前缀查找
        if original_tool_call_id and original_tool_call_id != "unknown":
            toolid_key = f"toolid_{original_tool_call_id}"
            if toolid_key in self.tool_call_id_map:
                found_id = self.tool_call_id_map[toolid_key]
                # 找到后删除映射，避免重复使用
                del self.tool_call_id_map[toolid_key]
                return found_id
        
        # 策略2：查找最新的序列号映射（倒序查找，因为tool_result通常紧跟tool_call）
        counter_key = f"{agent_name}:{tool_name}"
        if counter_key in self.tool_call_counters:
            current_seq = self.tool_call_counters[counter_key]
            
            # 从最新序列号开始倒序查找
            for seq_num in range(current_seq, 0, -1):
                seq_key = f"seq_{agent_name}:{tool_name}:{seq_num}"
                if seq_key in self.tool_call_id_map:
                    found_id = self.tool_call_id_map[seq_key]
                    # 找到后删除映射，避免重复使用
                    del self.tool_call_id_map[seq_key]
                    return found_id
        
        # 策略3：fallback，返回原始ID或unknown
        return original_tool_call_id if original_tool_call_id and original_tool_call_id != "unknown" else "unknown"
    
    def _cleanup_tool_call_mappings(self):
        """清理工具调用ID映射表，防止内存泄漏"""
        self.tool_call_id_map.clear()
        self.tool_call_counters.clear()
    
    def _is_thinking_content(self, message_chunk, metadata: Dict[str, Any]) -> bool:
        """判断内容是否为thinking内容（增强版：支持多种检测方式）"""
        # 1. 检查metadata中的标记
        if metadata.get('type') == 'thinking':
            return True
        
        # 2. 检查message_chunk的additional_kwargs中的reasoning_content（DeepSeek标准模式）
        if hasattr(message_chunk, 'additional_kwargs') and message_chunk.additional_kwargs:
            reasoning_content = message_chunk.additional_kwargs.get('reasoning_content')
            if reasoning_content:
                return True
        
        # 3. 检查是否为思考开始模型（可配置列表）
        # 注意：thinking_start_model的具体处理逻辑在_process_and_buffer_text_stream中
        # 这里只做初步检测，避免在show_thinking=False时跳过思考内容
        model_name = self._extract_model_name(metadata, message_chunk)
        if model_name and self._is_thinking_start_model(model_name):
            # 对于thinking_start_model，需要在流式处理中动态判断
            # 这里返回True是为了确保在show_thinking=False时能正确跳过
            return True
        
        return False
    
    def _extract_model_name(self, metadata: Dict[str, Any], message_chunk) -> str:
        """从各种可能的来源提取模型名称"""
        # 从 metadata 中获取
        if metadata and metadata.get('model_name'):
            return str(metadata.get('model_name', ''))
        
        # 从 message_chunk 的 response_metadata 中获取
        if hasattr(message_chunk, 'response_metadata') and message_chunk.response_metadata:
            model_name = message_chunk.response_metadata.get('model')
            if model_name:
                return str(model_name)
        
        # 从环境变量获取（默认模型）
        import os
        return str(os.getenv('OPENAI_MODEL', ''))
    
    def _get_thinking_start_models(self) -> List[str]:
        """获取思考开始模型列表（可配置）"""
        import os
        # 从环境变量获取配置，默认包含常见的思考开始模型
        default_models = 'qwq32b,deepseek-r1,qwen/qwen3-4b'
        models_str = os.getenv('THINKING_START_MODELS', default_models)
        return [model.strip().lower() for model in models_str.split(',') if model.strip()]
    
    def _is_thinking_start_model(self, model_name: str) -> bool:
        """判断模型是否在思考开始模型列表中（支持模糊匹配）"""
        if not model_name:
            return False
        
        model_name_lower = model_name.lower()
        thinking_models = self._get_thinking_start_models()
        
        # 支持模糊匹配：如果配置中的模型名在实际模型名中出现，则匹配
        for thinking_model in thinking_models:
            if thinking_model in model_name_lower:
                return True
        
        return False
    
    def _format_intent_analyzer_result(self, content: str, agent_name: str, namespace: Any):
        """格式化意图识别结果的输出"""
        try:
            # 尝试解析JSON内容
            intent_data = json.loads(content.strip())
            
            # 提取关键信息
            processing_mode = intent_data.get('processing_mode', '未知模式')
            primary_agent = intent_data.get('primary_agent', '未知')
            secondary_agents = intent_data.get('secondary_agents', [])
            complexity = intent_data.get('complexity', '未知')
            confidence = intent_data.get('confidence', 0.0)
            reasoning = intent_data.get('reasoning', '')
            estimated_duration = intent_data.get('estimated_duration', 0)
            
            # 代理名称映射
            agent_name_mapping = {
                'knowledge_agent': '知识专家',
                'mcp_agent': '工具专家',
                'multimodal_agent': '多模态专家',
                'custom_rag_agent': '自定义知识库专家',
                'simple_chat': '通用助手'
            }
            
            # 模式名称映射
            mode_mapping = {
                'supervisor_mode': '监督者模式',
                'swarm_mode': '群体协作模式',
                'single_agent': '单一代理模式'
            }
            
            # 复杂度映射
            complexity_mapping = {
                'low': '简单',
                'medium': '中等',
                'high': '复杂'
            }
            
            # 构建格式化的结果
            primary_agent_name = agent_name_mapping.get(primary_agent, primary_agent)
            secondary_agent_names = [agent_name_mapping.get(agent, agent) for agent in secondary_agents if agent]
            
            result_text = f"📋 **任务规划完成**\n\n"
            result_text += f"🎯 **处理模式**: {mode_mapping.get(processing_mode, processing_mode)}\n"
            result_text += f"👤 **主要负责**: {primary_agent_name}\n"
            
            if secondary_agent_names:
                # 过滤掉None值并转换为字符串
                valid_names = [str(name) for name in secondary_agent_names if name is not None]
                if valid_names:
                    result_text += f"🤝 **协作专家**: {', '.join(valid_names)}\n"
            
            result_text += f"📊 **复杂度**: {complexity_mapping.get(complexity, complexity)}\n"
            result_text += f"🎯 **置信度**: {confidence:.0%}\n"
            
            if estimated_duration > 0:
                result_text += f"⏱️ **预计用时**: {estimated_duration}秒\n"
            
            if reasoning:
                result_text += f"\n💭 **分析reasoning**: {reasoning}\n"
            
            result_text += "\n✅ 开始执行任务..."
            
            # 输出格式化的结果
            yield self._format_sse_data("intent_analysis", {
                "agent_name": agent_name,
                "namespace": namespace[0] if namespace else "main",
                "content": result_text,
                "data": intent_data
            })
            
        except json.JSONDecodeError:
            # 如果不是JSON格式，则作为普通文本输出
            yield self._format_sse_data("content", {
                "agent_name": agent_name,
                "content": f"📋 任务规划结果：{content}"
            })
    
    def _format_context_summarizer_result(self, agent_name: str, namespace: Any):
        """格式化上下文总结节点的输出"""
        
        # 发送总结完成的信息（开始消息已在流式处理中发送）
        yield self._format_sse_data("context_summary_complete", {
            "agent_name": agent_name,
            "namespace": namespace[0] if namespace else "main",
            "message": "✅ 总结完成"
        })
    
    def _detect_text_message_type(self, content: str) -> Dict[str, Any]:
        """检测文本模式消息的类型和内容
        
        Args:
            content: 仅包含完整事件标签的文本块
            
        Returns:
            包含消息类型和解析内容的字典
        """
        content = content.strip()
        
        # 这个函数现在只处理已知的、完整的事件标签
        # 检测推理标签 <think>...</think>
        think_match = re.search(r'<think>(.*?)</think>', content, re.DOTALL)
        if think_match:
            thinking_content = think_match.group(1).strip()
            return {
                "type": "reasoning",
                "content": thinking_content,
                "raw_content": content
            }
        
        # 检测工具调用 - 优先使用新格式 [TOOL_CALL]，兼容旧格式 <tool_call>
        # 修复：使用更精确的正则表达式匹配嵌套JSON
        tool_call_match = re.search(r'\[TOOL_CALL\]\s*(.*?)\s*\[/TOOL_CALL\]', content, re.DOTALL)
        if not tool_call_match:
            # 兼容旧格式，为了向后兼容保留
            tool_call_match = re.search(r'<tool_call>\s*(.*?)\s*</tool_call>', content, re.DOTALL)
        if tool_call_match:
            # 使用修复的JSON解析器处理可能截断的JSON
            json_str = tool_call_match.group(1)
            tool_data = text_parser._safe_json_parse(json_str)
            
            if tool_data and tool_data.get('name'):  # 确保解析成功且有工具名称
                result = {
                    "type": "tool_call",
                    "tool_name": tool_data.get('name', '未知工具'),
                    "reasoning": tool_data.get('reasoning', ''),
                    "raw_content": content
                }
                
                # 如果JSON中包含tool_id，也提取出来
                if tool_data.get('id'):
                    result["tool_id"] = tool_data.get('id')
                
                return result
            else:
                logger.warning(f"工具调用JSON解析失败或缺少工具名称: {json_str[:100]}...")
        
        # 检测Agent选择 - 优先使用新格式 [AGENT_SELECTION]，兼容旧格式 <agent_selection>
        # 修复：使用更精确的正则表达式匹配嵌套JSON
        agent_selection_match = re.search(r'\[AGENT_SELECTION\]\s*(.*?)\s*\[/AGENT_SELECTION\]', content, re.DOTALL)
        if not agent_selection_match:
            # 兼容旧格式，为了向后兼容保留
            agent_selection_match = re.search(r'<agent_selection>\s*(.*?)\s*</agent_selection>', content, re.DOTALL)
        if agent_selection_match:
            # 使用修复的JSON解析器处理可能截断的JSON
            json_str = agent_selection_match.group(1)
            selection_data = text_parser._safe_json_parse(json_str)
            
            if selection_data and selection_data.get('agent'):  # 确保解析成功且有Agent名称
                return {
                    "type": "agent_selection",
                    "agent": selection_data.get('agent', '未知Agent'),
                    "reasoning": selection_data.get('reasoning', ''),
                    "task": selection_data.get('task', ''),
                    "raw_content": content
                }
            else:
                logger.warning(f"Agent选择JSON解析失败或缺少Agent名称: {json_str[:100]}...")
        
        # 检测转交 - 优先使用新格式 [HANDOFF]，兼容旧格式 <handoff>
        handoff_match = re.search(r'\[HANDOFF\]\s*agent:\s*(\w+)\s*task:\s*([^\[]*?)\[/HANDOFF\]', content, re.DOTALL)
        if not handoff_match:
            # 兼容旧格式，为了向后兼容保留
            handoff_match = re.search(r'<handoff>\s*agent:\s*(\w+)\s*task:\s*([^<]*)</handoff>', content, re.DOTALL)
        if handoff_match:
            # 新格式是: [HANDOFF]agent: xxx task: xxx[/HANDOFF]
            # 旧格式是: <handoff>agent: xxx task: xxx</handoff>
            target_agent = handoff_match.group(1).strip()
            task_description = handoff_match.group(2).strip()
            return {
                "type": "handoff",
                "target_agent": target_agent,
                "task": task_description,
                "raw_content": content
            }
        
        # 检测CITE标记（来源引用）- 直接原文格式
        cite_match = re.search(r'\[CITE\]([^|]+)\|([^|]+)\|([^|]+)\|([^|]+)\|(.*?)\[/CITE\]', content, re.DOTALL)
        if cite_match:
            doc_name = cite_match.group(1).strip()
            chunk_id = cite_match.group(2).strip()
            similarity_score = cite_match.group(3).strip()
            chunk_index = cite_match.group(4).strip()
            original_text = cite_match.group(5).strip()
            
            return {
                "type": "cite",
                "doc_name": doc_name,
                "chunk_id": chunk_id,
                "similarity_score": float(similarity_score) if similarity_score.replace('.', '').isdigit() else 0.0,
                "chunk_index": chunk_index,
                "original_text": original_text,
                "raw_content": content
            }
        
        # 默认返回普通文本
        return {
            "type": "normal_text",
            "content": content,
            "raw_content": content
        }

    def _process_and_buffer_text_stream(self, content: str, agent_name: str, namespace: Any, 
                                        agent_message_started: Dict[str, bool], is_thinking: bool,
                                        processing_stats: Dict[str, Any], is_custom_rag_request: bool,
                                        model_name: str = ""):
        """
        使用缓冲区处理文本流，以正确解析被拆分的结构化消息，并实现真正的流式输出。
        
        支持新格式标签 [TAG] 和向后兼容旧格式标签 <tag>。
        支持无开始标签只有结束标签的思考内容检测。
        新增：qwq32b模型的特殊处理逻辑。
        """
        # 获取有界消息缓冲区
        message_buffer = self.resource_manager.get_message_buffer()
        
        # 检查是否为思考开始模型（可配置列表）
        is_thinking_start_model = model_name and self._is_thinking_start_model(model_name)
        
        # 统一的思考状态管理
        thinking_state_key = f"{agent_name}:thinking_state"
        current_thinking_state = message_buffer.get(thinking_state_key, "none")  # "none", "started", "ended"
        
        # 思考开始模型特殊处理：在没有检测到</think>之前，默认进入思考模式
        if is_thinking_start_model and not is_thinking:
            if current_thinking_state == "none" and "</think>" not in content:
                # 第一次进入思考模式
                is_thinking = True
                message_buffer.set(thinking_state_key, "started")
                # 移除thinking_start发送
            elif "</think>" in content and current_thinking_state == "started":
                # 遇到</think>标签，标记思考结束
                message_buffer.set(thinking_state_key, "ended")
                # 移除thinking_complete发送
            elif current_thinking_state == "started":
                # 继续思考模式
                is_thinking = True
        
        # 预读检测：检查新内容是否包含思考结束标签，用于无开始标签的思考内容检测
        contains_think_end = "</think>" in content
        current_buffer = message_buffer.get(f"{agent_name}:output", "") + content
        
        # 如果发现思考结束标签且当前不在思考模式，需要判断是否应该切换到思考模式
        if contains_think_end and not is_thinking and not is_thinking_start_model and current_thinking_state == "none":
            # 检查整个缓冲区中是否有对应的开始标签
            has_think_start = "<think>" in current_buffer
            
            if not has_think_start:
                # 没有开始标签，说明之前的内容都应该是思考内容
                # 切换到思考模式处理
                is_thinking = True
                message_buffer.set(thinking_state_key, "started")
                # 移除thinking_start发送
        
        message_key = f"{agent_name}:{'thinking' if is_thinking else 'output'}"
        
        # 特殊处理：intent_analyzer 不进行流式输出，而是缓存完整内容
        if agent_name == "intent_analyzer" and not is_thinking:
            # 检查是否是新消息的开始
            if message_key not in agent_message_started:
                agent_message_started[message_key] = True
                # 返回规划任务提示
                yield self._format_sse_data("agent_start", {
                    "agent_name": agent_name,
                    "namespace": namespace[0] if namespace else "main"
                })
                yield self._format_sse_data("content", {
                    "agent_name": agent_name,
                    "content": "🤖 正在分析您的需求并规划任务..."
                })
            
            # 累积内容到缓冲区，但不立即输出
            buffer = message_buffer.get(message_key, "") + content
            message_buffer.set(message_key, buffer)
            return  # 不进行流式输出，等待完整内容
        
        # 特殊处理：system_context_summarizer 显示总结状态
        if agent_name == "system_context_summarizer" and not is_thinking:
            # 检查是否是新消息的开始
            if message_key not in agent_message_started:
                agent_message_started[message_key] = True
                # 发送上下文总结开始状态
                yield self._format_sse_data("context_summary_start", {
                    "agent_name": agent_name,
                    "namespace": namespace[0] if namespace else "main",
                    "message": "🧠 正在总结对话内容..."
                })
            
            # 累积内容到缓冲区，但不输出实际内容（因为是内部处理）
            buffer = message_buffer.get(message_key, "") + content
            message_buffer.set(message_key, buffer)
            return  # 不进行流式输出，等待处理完成
        
        buffer = message_buffer.get(message_key, "") + content
        
        # 正则表达式，支持新格式 [TAG] 和兼容旧格式 <tag>
        # 优先匹配新格式，但保持向后兼容性
        new_format_pattern = r"\[(?P<ntag>TOOL_CALL|AGENT_SELECTION|HANDOFF|CITE)\](?:.|\n)*?\[/(?P=ntag)\]"
        # old_format_pattern = r"<(?P<tag>tool_call|agent_selection|handoff)>(?:.|\n)*?</(?P=tag)>"
        
        # 推理模型标签模式
        reasoning_pattern = r"<(?P<rtag>think)>(?:.|\n)*?</(?P=rtag)>"
        
        # 综合事件标签模式（包括推理标签）
        # event_tag_pattern = re.compile(f"(?:{new_format_pattern}|{old_format_pattern}|{reasoning_pattern})", re.DOTALL)
        event_tag_pattern = re.compile(f"(?:{new_format_pattern}|{reasoning_pattern})", re.DOTALL)
        
        while True:
            # 1. 优先处理在缓冲区开头的完整事件标签块
            match = event_tag_pattern.match(buffer)
            if match:
                block = match.group(0)
                
                # 检查是否是推理标签（<think>...</think>）
                if block.startswith('<think>') and block.endswith('</think>'):
                    # 这是完整的思考内容块，直接处理内部内容
                    thinking_content = block[7:-8]  # 移除<think>和</think>
                    if thinking_content.strip():
                        # 完整思考块处理：只输出内容，移除开始和结束标记
                        if current_thinking_state == "none":
                            message_buffer.set(thinking_state_key, "started")
                            # 移除thinking_start发送
                        # 直接作为thinking类型内容输出
                        yield self._format_sse_data("thinking", {
                            "agent_name": agent_name,
                            "content": thinking_content
                        })
                        # 移除thinking_complete发送
                        if current_thinking_state != "ended":
                            message_buffer.set(thinking_state_key, "ended")
                    buffer = buffer[match.end():]
                    continue
                else:
                    # 处理其他事件标签
                    message_info = self._detect_text_message_type(block)
                    
                    yield from self._generate_sse_from_message_info(
                        message_info, agent_name, namespace, agent_message_started, is_thinking,
                        processing_stats, is_custom_rag_request
                    )
                    
                    buffer = buffer[match.end():]
                    continue

            # 2. 检查是否有单独的<think>开始标签
            think_start_match = re.match(r"<think>(.*)$", buffer, re.DOTALL)
            if think_start_match:
                # 找到单独的<think>标签，开始思考模式
                if current_thinking_state == "none":
                    message_buffer.set(thinking_state_key, "started")
                    # 移除thinking_start发送
                
                # 移除<think>标签并切换到思考模式
                buffer = buffer[7:]  # 移除"<think>"
                message_buffer.set(message_key, buffer)
                
                # 递归处理剩余内容，标记为思考模式
                if buffer:
                    yield from self._process_and_buffer_text_stream(
                        "", agent_name, namespace, agent_message_started, 
                        True, processing_stats, is_custom_rag_request, model_name
                    )
                return

            # 3. 检查是否有单独的</think>结束标签（推理模型常见情况）
            think_end_match = re.match(r"</think>(.*)$", buffer, re.DOTALL)
            if think_end_match:
                # 找到单独的</think>标签，结束思考模式
                if current_thinking_state == "started":
                    message_buffer.set(thinking_state_key, "ended")
                    # 移除thinking_complete发送
                
                # 移除</think>标签
                remaining_content = think_end_match.group(1)
                buffer = buffer[9:]  # 移除"</think>"
                message_buffer.set(message_key, buffer)
                
                # 如果有剩余内容，切换到正常模式继续处理
                if remaining_content.strip():
                    yield from self._process_and_buffer_text_stream(
                        remaining_content, agent_name, namespace, agent_message_started,
                        False, processing_stats, is_custom_rag_request, model_name
                    )
                return  # 处理完成，退出
            
            # 4. 如果开头不是完整事件，则流式传输直到下一个 '<' 或 '[' 的所有内容
            next_tag_start = min(
                pos for pos in [buffer.find('<'), buffer.find('[')]
                if pos != -1
            ) if any(pos != -1 for pos in [buffer.find('<'), buffer.find('[')]) else -1
            
            # 确定可以安全流式传输的部分
            if next_tag_start == -1: # 缓冲区内没有 '<' 或 '['
                safe_text = buffer
                buffer = ""
            else:
                safe_text = buffer[:next_tag_start]
                buffer = buffer[next_tag_start:]

            # 剥离格式标签并流式传输
            if safe_text:
                # 简单替换，因为这些标签不应嵌套
                safe_text = safe_text.replace("<task_complete>", "").replace("</task_complete>", "")
                safe_text = safe_text.replace("[TASK_COMPLETE]", "").replace("[/TASK_COMPLETE]", "")
                # 处理推理模型的开始标签
                safe_text = safe_text.replace("<think>", "")
                
                if safe_text:
                    yield from self._generate_sse_from_message_info(
                        {"type": "normal_text", "content": safe_text},
                        agent_name, namespace, agent_message_started, is_thinking,
                        processing_stats, is_custom_rag_request
                    )
            
            # 如果缓冲区为空或不再以'<'或'['开头（不太可能发生，但作为安全检查），则退出
            if not buffer or not (buffer.startswith('<') or buffer.startswith('[')):
                break
            
            # 检查剩余部分是否是完整的格式标签，如果是，则剥离并继续
            stripped = False
            for tag in ["<task_complete>", "</task_complete>", "[TASK_COMPLETE]", "[/TASK_COMPLETE]", "<think>"]:
                if buffer.startswith(tag):
                    buffer = buffer[len(tag):]
                    stripped = True
                    break
            
            if stripped:
                continue

            # 如果缓冲区剩下以'<'或'['开头但不是完整事件标签的内容，则退出循环等待更多数据
            if not event_tag_pattern.match(buffer):
                break
        
        # 更新缓冲区，只保留未处理的部分
        message_buffer.set(message_key, buffer)


    def _generate_sse_from_message_info(self, message_info: Dict[str, Any], agent_name: str, namespace: Any, 
                                        agent_message_started: Dict[str, bool], is_thinking: bool,
                                        processing_stats: Dict[str, Any], is_custom_rag_request: bool):
        """根据解析后的消息信息生成SSE事件。"""
        message_type = message_info.get("type", "normal_text")
        
        # 检查是否是新消息的开始
        message_key = f"{agent_name}:{'thinking' if is_thinking else 'output'}"
        if message_key not in agent_message_started:
            agent_message_started[message_key] = True
            if is_thinking:
                # 思考模式：由上层函数统一管理thinking_start，这里不重复发送
                pass
            else:
                # 正常模式：发送agent_start事件
                yield self._format_sse_data("agent_start", {
                    "agent_name": agent_name,
                    "namespace": namespace[0] if namespace else "main"
                })
        
        # 根据消息类型生成用户友好的输出
        if message_type == "reasoning":
            # 处理推理内容 - 直接作为thinking类型事件输出
            thinking_content = message_info.get("content", "")
            if thinking_content:
                # 推理内容的开始和结束由上层函数统一管理，这里只输出内容
                yield self._format_sse_data("thinking", {
                    "agent_name": agent_name,
                    "content": thinking_content
                })
        elif message_type == "tool_call":
            tool_name = message_info["tool_name"]
            if "tools_called" in processing_stats:
                processing_stats["tools_called"].add(tool_name)
            
            friendly_tool_names = {
                "search_knowledge_base": "知识库搜索", "search_custom_knowledge_base": "自定义知识库搜索",
                "handoff_to_agent": "任务转交", "web_search": "网络搜索",
                "file_read": "文件读取", "image_analysis": "图像分析"
            }
            friendly_name = friendly_tool_names.get(tool_name, tool_name)
            
            user_message = f"🔍 正在搜索您的自定义知识库..." if is_custom_rag_request and tool_name in ["search_custom_knowledge_base", "search_knowledge_base"] else f"🔧 正在使用 {friendly_name}..."
            if is_custom_rag_request and tool_name in ["search_custom_knowledge_base", "search_knowledge_base"]:
                processing_stats["custom_rag_used"] = True

            # 为文本解析路径的工具调用也生成tool_call_id
            tool_id = message_info.get("tool_id")  # 从消息解析中获取tool_id（如果有的话）
            tool_call_id = self._generate_tool_call_id(agent_name, tool_name, tool_id)
            
            # 建立映射关系（用于后续的tool_result事件）
            unique_mapping_key = self._generate_unique_mapping_key(agent_name, tool_name, tool_id)
            self.tool_call_id_map[unique_mapping_key] = tool_call_id
            
            # 调试信息：输出映射建立的详细信息
            logger.info(f"🔧 Tool Call Mapping Created - Agent: {agent_name}, Tool: {tool_name}")
            logger.info(f"   Generated tool_call_id: {tool_call_id}")
            logger.info(f"   Original tool_id: {tool_id}")
            logger.info(f"   Mapping key: {unique_mapping_key}")
            logger.info(f"   Mapping table size: {len(self.tool_call_id_map)}")

            yield self._format_sse_data("tool_call", {
                "agent_name": agent_name, 
                "content": user_message, 
                "tool_name": tool_name,
                "tool_call_id": tool_call_id
            })
            
        elif message_type == "agent_selection":
            target_agent = message_info["agent"]
            friendly_agent_names = {
                "knowledge_agent": "知识专家", "simple_chat": "通用助手", "mcp_agent": "工具专家",
                "multimodal_agent": "多模态专家", "custom_rag_agent": "自定义知识库专家"
            }
            friendly_name = friendly_agent_names.get(target_agent, target_agent)
            task = message_info.get("task", "")
            user_message = f"📋 将任务分配给 {friendly_name}，任务描述：{task}"
            yield self._format_sse_data("agent_assignment", {"agent_name": agent_name, "content": user_message, "target_agent": target_agent, "task": task})
            
        elif message_type == "handoff":
            target_agent = message_info["target_agent"]
            friendly_agent_names = {
                "knowledge_agent": "知识专家", "simple_chat": "通用助手", "mcp_agent": "工具专家",
                "multimodal_agent": "多模态专家", "custom_rag_agent": "自定义知识库专家"
            }
            friendly_name = friendly_agent_names.get(target_agent, target_agent)
            user_message = f"↗️ 正在转交给 {friendly_name}..."
            yield self._format_sse_data("agent_handoff", {"agent_name": agent_name, "content": user_message, "target_agent": target_agent})
                
        elif message_type == "cite":
            # 处理CITE标记 - 简化处理，直接使用模型输出的原文
            doc_name = message_info.get("doc_name", "未知文档")
            chunk_id = message_info.get("chunk_id", "未知段落")
            similarity_score = message_info.get("similarity_score", 0.0)
            original_text = message_info.get("original_text", "")
            
            # 生成引用ID（简单递增数字）
            self.cite_counter += 1
            cite_id = str(self.cite_counter)
            
            yield self._format_sse_data("cite", {
                "agent_name": agent_name,
                "cite_id": cite_id,
                "doc_name": doc_name,
                "chunk_id": chunk_id,
                "similarity_score": similarity_score,
                "original_text": original_text,
                "display_text": f"[{cite_id}]",
                "content": message_info.get("raw_content", "")
            })
                
        elif message_type == "normal_text":
            content = message_info.get("content", "")
            if content: # 不再使用strip()，以允许流式传输空格等
                event_type = "thinking" if is_thinking else "content"
                yield self._format_sse_data(event_type, {"agent_name": agent_name, "content": content})
    
    def _format_sse_data(self, event_type: str, data: Any) -> str:
        """格式化SSE数据"""
        sse_data = {
            "type": event_type,
            "data": data
        }
        return f"data: {json.dumps(sse_data, ensure_ascii=False)}\n\n"
    
    async def _build_human_message(self, request, relevant_memories=None) -> HumanMessage:
        """构建包含文本和图片的人类消息"""
        # 构建包含长期记忆的问题文本
        question_text = request.question
        
        # 如果有相关记忆，将其添加到上下文中
        if relevant_memories and len(relevant_memories) > 0:
            memory_context = self.long_term_memory.format_memories_for_context(relevant_memories)
            question_text = f"{memory_context}\n\n# 用户问题\n{request.question}"
            logger.info(f"已将 {len(relevant_memories)} 条长期记忆添加到上下文中")
        
        # 检查是否有图片文件
        uploaded_files = getattr(request, 'uploaded_files', [])
        has_images = any(file_info.is_image for file_info in uploaded_files if file_info.is_image)
        
        if has_images:
            # 有图片时使用多模态格式
            content = [{"type": "text", "text": question_text}]
            
            # 添加图片内容
            for file_info in uploaded_files:
                if file_info.is_image:
                    file_path = file_info.file_path
                    if file_path and os.path.exists(file_path):
                        try:
                            # 异步读取图片并转换为base64
                            import base64
                            import aiofiles
                            
                            async with aiofiles.open(file_path, "rb") as image_file:
                                image_data = base64.b64encode(await image_file.read()).decode('utf-8')
                            
                            # 获取文件扩展名来确定MIME类型
                            file_ext = os.path.splitext(file_path)[1].lower()
                            # 使用统一的Qwen2.5-VL支持的图片格式MIME类型映射
                            from rag_chat.jiliang_chat_refactored import SUPPORTED_IMAGE_MIME_TYPES
                            mime_type = SUPPORTED_IMAGE_MIME_TYPES.get(file_ext, 'image/jpeg')
                            
                            # 添加图片内容
                            content.append({
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{mime_type};base64,{image_data}"
                                }
                            })
                            logger.info(f"已添加图片到消息中: {file_info.filename}")
                        except Exception as e:
                            logger.error(f"处理图片文件失败 {file_path}: {e}")
        else:
            # 没有图片时使用纯文本格式
            content = question_text
        
        # 将详细的文件信息添加到additional_kwargs中，用于历史记录显示
        additional_kwargs = {}
        if uploaded_files:
            file_info_list = []
            for file_info in uploaded_files:
                # 计算文件大小的友好显示格式
                def format_file_size(size_bytes):
                    if size_bytes < 1024:
                        return f"{size_bytes}B"
                    elif size_bytes < 1024 * 1024:
                        return f"{size_bytes / 1024:.1f}KB"
                    else:
                        return f"{size_bytes / (1024 * 1024):.1f}MB"
                
                # 获取文件扩展名
                file_ext = os.path.splitext(file_info.filename)[1].upper().lstrip('.')
                file_type = file_ext if file_ext else "未知格式"
                
                file_info_dict = {
                    "filename": file_info.filename,
                    "file_size": file_info.file_size,
                    "file_size_formatted": format_file_size(file_info.file_size),
                    "content_type": file_info.content_type,
                    "file_type": file_type,
                    "is_image": file_info.is_image,
                    "upload_time": file_info.upload_time,
                    "file_category": "图片文件" if file_info.is_image else "文档文件",
                    "file_id": file_info.file_id
                }
                file_info_list.append(file_info_dict)
            
            additional_kwargs["uploaded_files"] = file_info_list
            additional_kwargs["file_count"] = len(file_info_list)
            additional_kwargs["has_files"] = True
        
        mcp_ids = getattr(request, "mcp_ids", [])
        extral_rag_ids = getattr(request, "extral_rag_ids", [])
        additional_kwargs["mcp_ids"] = mcp_ids
        additional_kwargs["extral_rag_ids"] = extral_rag_ids
        
        # 确保content符合HumanMessage的类型要求
        if isinstance(content, list):
            # 多模态内容：确保类型正确
            try:
                # 验证list中的项目类型
                formatted_content = []
                for item in content:
                    if isinstance(item, dict):
                        formatted_content.append(item)
                    else:
                        # 转换非字典项为字典格式
                        formatted_content.append({"type": "text", "text": str(item)})
            except Exception:
                # 如果处理失败，转为纯文本
                formatted_content = str(content)
        else:
            # 纯文本内容：str
            formatted_content = str(content)
        
        return HumanMessage(content=formatted_content, additional_kwargs=additional_kwargs)
    
    def _get_model_desc(self, model_name: str) -> str:
        """获取模型描述"""
        if not model_name:
            return "DeepSeek"
        
        # 简单的模型名称映射
        model_mapping = {
            "deepseek-chat": "DeepSeek",
            "gpt-4": "GPT-4",
            "gpt-3.5-turbo": "GPT-3.5",
            "claude-3": "Claude-3"
        }
        
        return model_mapping.get(model_name, model_name)
    
    async def _extract_final_response(self, selected_graph, config) -> Optional[str]:
        """提取最终的AI响应"""
        try:
            # 优先使用异步方法，回退到同步方法
            if hasattr(selected_graph, 'aget_state'):
                final_state = await selected_graph.aget_state(config)
            elif hasattr(selected_graph, 'get_state'):
                final_state = selected_graph.get_state(config)
            else:
                return None
                
            if final_state and hasattr(final_state, 'values') and 'messages' in final_state.values:
                messages = final_state.values['messages']
                # 找到最后一条AI消息
                for msg in reversed(messages):
                    if isinstance(msg, AIMessage):
                        return msg.content if isinstance(msg.content, str) else str(msg.content)
            return None
        except Exception as e:
            logger.warning(f"提取最终响应失败: {e}")
            return None
    
    async def _store_memory_background(self, userid: str, qa_id: str, user_question: str, 
                                     ai_response: str, request: Any, api_key: str = "", username: str = "") -> None:
        """后台异步存储记忆"""
        try:
            # 构建要存储的消息列表
            messages_to_store = [
                {"role": "user", "content": user_question},
                {"role": "assistant", "content": ai_response}
            ]
            
            # 确定对话类型
            chat_type = 0  # 默认普通问答
            files_info = []  # 用于存储文件信息
            if getattr(request, 'images_ids', []):
                chat_type = 1  # 文件问答
                # 构造记忆存储的图片信息
                for image_id in getattr(request, 'images_ids', []):
                    files_info.append({"file_id": image_id, 
                                       "file_type": 0,
                                       "file_url": f"{FILE_SERVER_URL}/file/storage/download/{image_id}"})
            elif getattr(request, 'extral_rag_ids', []):
                chat_type = 2  # 知识库问答
            
            # 存储记忆
            if api_key and username:
                stored_memories = await self.long_term_memory.store_conversation(
                    userid=userid,
                    qa_id=qa_id,
                    messages=messages_to_store,
                    api_key=api_key,
                    username=username,
                    chat_type=chat_type,
                    files=files_info
                )
            else:
                logger.warning("缺少API密钥或用户名，跳过后台记忆存储")
                return
            
            if stored_memories:
                logger.info(f"后台记忆存储成功: 用户={userid}, 存储了 {len(stored_memories)} 条记忆")
            else:
                logger.warning(f"后台记忆存储失败: 用户={userid}")
                
        except Exception as e:
            logger.error(f"后台记忆存储异常: {e}")
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "service_name": "MultiAgentService",
            "version": "2.7.0", # 版本升级，修复LangGraph流式输出重复问题
            "initialized": self.initialized,
            "function_call_graph_available": self.graph is not None,
            "text_parsing_graph_available": self.text_graph is not None,
            "supported_execution_modes": ["function_call", "text_parsing", "react_mode"],
            "supported_tag_formats": {
                "primary": "bracket_format",  # [TAG] 和 [/TAG]
                "legacy": "xml_format"        # <tag> 和 </tag> (兼容性)
            },
            "features": [
                "true_token_level_streaming",
                "robust_event_tag_handling",
                "formatting_tag_stripping",
                "custom_rag_support", 
                "agent_performance_monitoring",
                "user_friendly_output",
                "backward_compatibility",  # 向后兼容特性
                "intent_analyzer_formatting",  # 意图识别结果格式化
                "reasoning_model_streaming_support",  # 推理模型流式输出支持
                "unified_sse_event_types",  # SSE事件类型统一化
                "duplicate_message_prevention"  # 新增：重复消息防护
            ],
            "all_sse_events": [
                # 系统状态事件 (3个)
                "status", "completion", "error",
                # Agent生命周期事件 (2个)
                "agent_start", "agent_complete",
                # 思考相关事件 (1个) - 移除开始和结束标记
                "thinking",
                # 内容输出事件 (1个)
                "content",
                # 工具相关事件 (2个)
                "tool_call", "tool_result",
                # Agent协作事件 (2个)
                "agent_assignment", "agent_handoff",
                # 特殊功能事件 (5个)
                "intent_analysis", "context_summary_start", "context_summary_complete", "human_message", "cite"
            ],
            "tag_migration_status": "completed_with_compatibility",  # 标签迁移状态
            "special_agent_handling": {
                "intent_analyzer": "non_streaming_formatted_output"  # 特殊代理处理说明
            },
            "stream_deduplication": {
                "method": "message_id_and_finish_reason_based",  # 去重方法
                "description": "基于消息ID和finish_reason进行重复消息检测和过滤",
                "langgraph_issue": "LangGraph在使用checkpointer时会在流式结束后重新发送完整消息",
                "solution": "使用消息ID和完成状态标识符进行去重，同时简化stream_mode为'messages'模式"
            }
        }

    def _init_conversation_manager(self):
        """初始化对话历史管理器"""
        try:
            # 数据库配置（从环境变量或配置文件获取）
            db_config = {
                'host': os.getenv('DM_HOST', '*************'),
                'port': int(os.getenv('DM_PORT', 5236)),
                'user': os.getenv('DM_USER', 'SX_NACOS'),
                'password': os.getenv('DM_PASSWORD', 'Gznt_85535888'),
                'database': os.getenv('DM_DATABASE', 'SX_JILIANG'),
                'auto_commit': False
            }

            self.conversation_manager = SimpleConversationManager(db_config)
            logger.info("对话历史管理器初始化成功")

        except Exception as e:
            logger.error(f"对话历史管理器初始化失败: {e}")
            self.conversation_manager = None

    def _init_conversation_data(self, thread_id: str, user_id: str, question: str, qa_id: str):
        """初始化当前对话数据收集器"""
        self.current_conversation_data = {
            "thread_id": thread_id,
            "messages": [
                {
                    "role": "user",
                    "files": [],
                    "rag_ids": [],
                    "mcp_ids": "[]",
                    "model_name": "",
                    "qa_id": qa_id,
                    "content": question,
                    "timestamp": datetime.now().isoformat(),
                    "message_id": f"user_{qa_id}"
                }
            ],
            "total_count": 1,
            "metadata": {
                "thread_id": thread_id,
                "user_id": user_id,
                "qa_id": qa_id,
                "status": "processing",
                "started_at": datetime.now().isoformat()
            }
        }

        # 当前assistant消息（用于收集AI回复）
        self.current_assistant_message = {
            "role": "assistant",
            "tokens": {},
            "model_name": "",
            "qa_id": qa_id,
            "timestamp": datetime.now().isoformat(),
            "list": []
        }

    def _add_ai_response_item(self, item_type: str, agent_name: str, agent_title: str,
                             content: str = "", think: str = "", tools: Optional[List] = None,
                             tools_result: Optional[Dict] = None):
        """添加AI回复项到当前对话数据"""
        if not hasattr(self, 'current_assistant_message'):
            return

        ai_item = {
            "type": item_type,
            "agent_name": agent_name,
            "agent_title": agent_title,
            "content": content,
            "think": think,
            "tools": tools or [],
            "tools_result": tools_result or {},
            "timestamp": datetime.now().isoformat()
        }

        self.current_assistant_message["list"].append(ai_item)

    async def _save_conversation_history_async(self, thread_id: str, user_id: str, username: str,
                                             processing_mode: str, primary_agent: str, token_stats: dict):
        """异步保存对话历史"""
        if not self.conversation_manager or not hasattr(self, 'current_conversation_data'):
            logger.warning("对话历史管理器未初始化或对话数据不存在，跳过保存")
            return

        try:
            # 完成当前assistant消息
            if hasattr(self, 'current_assistant_message') and self.current_assistant_message["list"]:
                # 更新token统计
                self.current_assistant_message["tokens"] = token_stats

                # 添加到对话数据中
                self.current_conversation_data["messages"].append(self.current_assistant_message)
                self.current_conversation_data["total_count"] = len(self.current_conversation_data["messages"])

            # 更新元数据
            self.current_conversation_data["metadata"].update({
                "status": "completed",
                "completed_at": datetime.now().isoformat(),
                "processing_mode": processing_mode,
                "primary_agent": primary_agent,
                "total_tokens": token_stats.get("total_tokens", 0)
            })

            # 获取下一个轮次号
            round_number = self.conversation_manager.get_next_round_number(thread_id)

            # 保存到数据库
            success = self.conversation_manager.save_conversation_round(
                thread_id=thread_id,
                user_id=user_id,
                username=username,
                round_number=round_number,
                conversation_data=self.current_conversation_data,
                processing_mode=processing_mode,
                primary_agent=primary_agent,
                status="COMPLETED"
            )

            if success:
                logger.info(f"对话历史保存成功: {thread_id}, 轮次: {round_number}")
            else:
                logger.error(f"对话历史保存失败: {thread_id}")

        except Exception as e:
            logger.error(f"保存对话历史异常: {e}")
        finally:
            # 清理当前对话数据
            self.current_conversation_data = {}
            if hasattr(self, 'current_assistant_message'):
                delattr(self, 'current_assistant_message')


# 全局服务实例
multi_agent_service = MultiAgentService()


async def initialize_multi_agent_service(
    checkpointer: Any = None,
    mcp_router: Any = None,
    mcp_simple_router: Any = None
):
    """初始化多智能体服务"""
    await multi_agent_service.initialize(
        checkpointer=checkpointer,
        mcp_router=mcp_router,
        mcp_simple_router=mcp_simple_router
    )


def get_multi_agent_service() -> MultiAgentService:
    """获取多智能体服务实例"""
    return multi_agent_service
