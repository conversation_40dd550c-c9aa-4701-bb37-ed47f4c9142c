from typing import Generic, TypeVar, List, Dict, Any
from typing import Optional

from pydantic import BaseModel, Field

T = TypeVar('T')

class R(BaseModel, Generic[T]):

    class Config:
        populate_by_name = True  # 允许通过字段名填充数据

    code: str = Field(..., alias='code')
    error_code: Optional[str] = Field(None, alias='errorCode')
    msg: str = Field(..., alias='msg')
    error_msg: Optional[str] = Field(None, alias='errorMsg')
    data: Optional[T] = Field(None, alias='data')

    @classmethod
    def ok(cls, data: T = None, message: str = "Success") -> 'R':
        return R(code='200', msg=message, data=data)

    @classmethod
    def error(cls, error_code: str, error_message: str, message: str = '系统异常') -> 'R':
        r = R(code='500', msg=message)
        r.error_code = error_code
        r.error_msg = error_message
        return r

class RagQARequest(BaseModel):
    question: str
    qa_id: str = None
    model: str = None
    temperature: Optional[float] = 0.6
    mcp_ids: Optional[List] = None
    extral_rag_ids: Optional[List] = None
    function_type: Optional[str] = None  # 功能类型英文代码
    function_name: Optional[str] = None  # 功能名称中文

class UploadedFileInfo(BaseModel):
    """上传文件信息"""
    file_path: str = Field(..., description="文件本地路径")
    filename: str = Field(..., description="原始文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    content_type: str = Field(..., description="文件MIME类型")
    is_image: bool = Field(..., description="是否为图片文件")
    upload_time: str = Field(..., description="上传时间")
    file_id: str = Field(..., description="文件的obs存储id")

class ThreadHistoryRequest(BaseModel):
    """获取线程历史消息的请求模型"""
    user_id: str = Field(..., description="用户ID")
    qa_id: str = Field(..., description="会话ID") 
    limit: Optional[int] = Field(default=100, description="返回消息数量限制，默认100条")
    include_metadata: Optional[bool] = Field(default=False, description="是否包含元数据信息")

class ThreadHistoryResponse(BaseModel):
    """线程历史消息的响应模型"""
    thread_id: str = Field(..., description="线程ID")
    messages: List[Dict[str, Any]] = Field(..., description="消息列表")
    total_count: int = Field(..., description="消息总数")
    metadata: Optional[Dict[str, Any]] = Field(None, description="线程元数据")

class MultiAgentQARequest(BaseModel):
    question: str
    qa_id: Optional[str] = None
    thread_id: Optional[str] = None  # 对话线程ID，用于对话历史管理
    model: Optional[str] = None
    temperature: Optional[float] = 0.6
    top_p: Optional[float] = 0.9
    top_k: Optional[int] = 20
    mcp_ids: Optional[List] = None
    extral_rag_ids: Optional[List] = None
    files_ids: Optional[List] = None
    images_ids: Optional[List] = None
    uploaded_files: Optional[List[UploadedFileInfo]] = Field(default=[], description="上传的文件信息列表")

class FileQARequest(BaseModel):
    question: str
    file_id: Optional[str] = None
    file_ids: Optional[List[str]] = None
    qa_id: Optional[str] = None
    model: str = None
    temperature: Optional[float] = 0.6  # 温度参数默认值为0.6
    chat_type: int = 1  # 聊天类型：0-普通问答，1-文件问答，2-图片问答
    image_data: str = None  # 图片数据（base64编码）
    image_url: str = None  # 图片URL
    # 新增统一问答类型字段
    qa_type: Optional[int] = None  # 问答类型：0-普通问答，1-文件问答，2-图片问答
    function_type: Optional[str] = None  # 功能类型英文代码
    function_name: Optional[str] = None  # 功能名称中文（可选，会自动查询）

class TranslateRequest(BaseModel):
    file_id: Optional[str] = None
    source_lang: str = "auto"
    target_lang: str = "auto"
    model: Optional[str] = None
    function_type: Optional[str] = None  # 功能类型英文代码
    function_name: Optional[str] = None  # 功能名称中文

class SessionQueryRequest(BaseModel):
    userid: str
    page: int = 1
    page_size: int = 20

class SessionResponse(BaseModel):
    qa_id: str
    question: str
    createtime: str

# 请求体模型
class QARecordQueryRequest(BaseModel):
    userid: str
    chat_type: int
    page: int
    page_size: int
    qa_id: str

# 响应字段模型
class QARecordResponse(BaseModel):
    id: int
    answer: Optional[str]
    question: Optional[str]
    reasoning: Optional[str]
    createtime: str
    CHAT_TYPE: int
    userid: str
    # apikey: Optional[str]

# 分页业务数据模型
class PaginatedData(BaseModel):
    records: List[QARecordResponse]
    total: int
    page: int
    page_size: int

# 统一API响应模型
class ApiResponse(BaseModel):
    code: str = "200"
    msg: str = "success"
    errorCode: str = ""
    errorMsg: str = ""
    data: Optional[Dict[str, Any]] = None
    exception: str = ""

# 检索
class SessionSearchRequest(BaseModel):
    userid: str
    keyword:str
    page: int = 1
    page_size: int = 20

class MessageRecordsRequest(BaseModel):
    userid: str
    qa_id: str

class SessionDeleteRequest(BaseModel):
    userid: str
    chat_type: int = None
    qa_id: str = None
    delete_all: bool = False

class MessageDeleteRequest(BaseModel):
    userid: str
    chat_type: int
    qa_id: str
    message_id: str = None
    message_ids: List[str]

class ListMcpToolsRequest(BaseModel):
    mcp_ids: List[int] = Field(..., description="MCP服务器ID列表")

# 图片问答请求模型
class ImageQARequest(BaseModel):
    question: str
    image_data: Optional[str] = None  # base64编码的图片数据
    image_url: Optional[str] = None   # 图片URL
    qa_id: Optional[str] = None
    model: Optional[str] = None
    temperature: Optional[float] = 0.6
    function_type: Optional[str] = None  # 功能类型英文代码
    function_name: Optional[str] = None  # 功能名称中文

class FunctionStatsQueryRequest(BaseModel):
    """功能统计查询请求模型"""
    user_id: Optional[str] = None
    function_type: Optional[str] = None
    start_date: Optional[str] = None  # YYYY-MM-DD格式
    end_date: Optional[str] = None
    page: int = 1
    page_size: int = 20

class FunctionStatsResponse(BaseModel):
    """功能统计响应模型"""
    function_type: str
    function_name: str
    total_calls: int
    unique_users: int
    avg_duration: Optional[float] = None
    success_calls: int = 0
    error_calls: int = 0
    error_rate: Optional[float] = None

class UserStatsResponse(BaseModel):
    """用户统计响应模型"""
    user_id: str
    function_type: str
    function_name: str
    call_count: int
    avg_duration: Optional[float] = None
    last_used: Optional[str] = None

class FunctionConfigResponse(BaseModel):
    """功能配置响应模型"""
    function_type: str
    function_name: str
    api_path: str
    category: Optional[str] = None
    description: Optional[str] = None

# WPS专用请求模型
class WpsQARequest(RagQARequest):
    """WPS问答请求模型"""
    seed: Optional[int] = None  # WPS特有的seed参数

# PDF翻译请求模型
class PdfTranslateRequest(BaseModel):
    """PDF翻译请求模型"""
    file_id: str
    source_lang: str = "auto"
    target_lang: str = "auto"
    model: Optional[str] = None
    function_type: Optional[str] = None  # 功能类型英文代码（docTranslatePlus）
    function_name: Optional[str] = None  # 功能名称中文