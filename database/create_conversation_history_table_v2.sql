-- 对话历史存储表创建脚本（方案B：优化的单表方案）
-- 数据库：达梦数据库

-- 创建对话历史表（按轮次存储）
CREATE TABLE SX_CONVERSATION_HISTORY (
    -- 主键
    ID              VARCHAR(64) PRIMARY KEY,               -- 记录唯一ID (UUID)
    
    -- 对话标识
    THREAD_ID       VARCHAR(64) NOT NULL,                  -- 对话线程ID
    USER_ID         VARCHAR(64) NOT NULL,                  -- 用户ID
    USERNAME        VARCHAR(128),                          -- 用户名
    
    -- 轮次信息
    ROUND_NUMBER    INTEGER NOT NULL,                      -- 对话轮次（1,2,3...）
    
    -- 对话数据
    CONVERSATION_JSON CLOB NOT NULL,                       -- 本轮对话JSON数据
    
    -- 缓存统计信息（避免解析JSON）
    MESSAGE_COUNT   INTEGER DEFAULT 0,                     -- 本轮消息数量
    TOKEN_COUNT     INTEGER DEFAULT 0,                     -- 本轮token数量
    USER_MSG_COUNT  INTEGER DEFAULT 0,                     -- 本轮用户消息数
    AI_MSG_COUNT    INTEGER DEFAULT 0,                     -- 本轮AI消息数
    TOOL_CALL_COUNT INTEGER DEFAULT 0,                     -- 本轮工具调用数
    
    -- 对话元数据
    PROCESSING_MODE VARCHAR(64),                           -- 处理模式
    PRIMARY_AGENT   VARCHAR(128),                          -- 主要agent
    STATUS          VARCHAR(32) DEFAULT 'ACTIVE',          -- 轮次状态
    
    -- 时间戳
    CREATED_AT      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 创建时间
    UPDATED_AT      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- 更新时间
    
    -- 软删除
    IS_DELETED      CHAR(1) DEFAULT '0',                   -- 是否删除：0-否，1-是
    
    -- 唯一约束
    CONSTRAINT UK_THREAD_ROUND UNIQUE (THREAD_ID, ROUND_NUMBER)
);

-- 添加表注释
COMMENT ON TABLE SX_CONVERSATION_HISTORY IS '对话历史存储表（按轮次存储）';

-- 添加字段注释
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.ID IS '记录唯一ID (UUID)';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.THREAD_ID IS '对话线程ID';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.USER_ID IS '用户ID';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.USERNAME IS '用户名';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.ROUND_NUMBER IS '对话轮次（1,2,3...）';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.CONVERSATION_JSON IS '本轮对话JSON数据';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.MESSAGE_COUNT IS '本轮消息数量';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.TOKEN_COUNT IS '本轮token数量';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.USER_MSG_COUNT IS '本轮用户消息数';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.AI_MSG_COUNT IS '本轮AI消息数';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.TOOL_CALL_COUNT IS '本轮工具调用数';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.PROCESSING_MODE IS '处理模式';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.PRIMARY_AGENT IS '主要agent';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.STATUS IS '轮次状态';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.CREATED_AT IS '创建时间';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.UPDATED_AT IS '更新时间';
COMMENT ON COLUMN SX_CONVERSATION_HISTORY.IS_DELETED IS '是否删除：0-否，1-是';

-- 创建索引
-- 主查询索引：按线程ID和轮次查询
CREATE INDEX IDX_THREAD_ROUND ON SX_CONVERSATION_HISTORY(THREAD_ID, ROUND_NUMBER);

-- 用户查询索引：查询用户的所有对话
CREATE INDEX IDX_USER_TIME ON SX_CONVERSATION_HISTORY(USER_ID, CREATED_AT DESC);

-- 用户名查询索引
CREATE INDEX IDX_USERNAME_TIME ON SX_CONVERSATION_HISTORY(USERNAME, CREATED_AT DESC);

-- 软删除过滤索引
CREATE INDEX IDX_NOT_DELETED ON SX_CONVERSATION_HISTORY(IS_DELETED, THREAD_ID, ROUND_NUMBER);

-- 状态查询索引
CREATE INDEX IDX_STATUS_TIME ON SX_CONVERSATION_HISTORY(STATUS, CREATED_AT DESC);

-- 复合索引：用户+线程+轮次（最常用的查询模式）
CREATE INDEX IDX_USER_THREAD_ROUND ON SX_CONVERSATION_HISTORY(USER_ID, THREAD_ID, ROUND_NUMBER);

-- 添加索引注释
COMMENT ON INDEX IDX_THREAD_ROUND IS '线程ID和轮次查询索引';
COMMENT ON INDEX IDX_USER_TIME IS '用户查询索引（按时间倒序）';
COMMENT ON INDEX IDX_USERNAME_TIME IS '用户名查询索引';
COMMENT ON INDEX IDX_NOT_DELETED IS '软删除过滤索引';
COMMENT ON INDEX IDX_STATUS_TIME IS '状态查询索引';
COMMENT ON INDEX IDX_USER_THREAD_ROUND IS '用户+线程+轮次复合索引';

-- 创建触发器（自动更新UPDATED_AT字段）
CREATE OR REPLACE TRIGGER TRG_CONV_HISTORY_UPDATE
    BEFORE UPDATE ON SX_CONVERSATION_HISTORY
    FOR EACH ROW
BEGIN
    :NEW.UPDATED_AT := CURRENT_TIMESTAMP;
END;
/

COMMENT ON TRIGGER TRG_CONV_HISTORY_UPDATE IS '自动更新UPDATED_AT字段的触发器';

-- 示例查询语句
/*
-- 1. 获取指定对话的所有轮次（按轮次顺序）
SELECT ROUND_NUMBER, CONVERSATION_JSON, MESSAGE_COUNT, TOKEN_COUNT, CREATED_AT
FROM SX_CONVERSATION_HISTORY 
WHERE THREAD_ID = 'jiliang--abcde' AND IS_DELETED = '0'
ORDER BY ROUND_NUMBER;

-- 2. 获取用户的所有对话线程（去重）
SELECT DISTINCT THREAD_ID, MIN(CREATED_AT) as first_round_time, MAX(CREATED_AT) as last_round_time,
       COUNT(*) as total_rounds, SUM(MESSAGE_COUNT) as total_messages, SUM(TOKEN_COUNT) as total_tokens
FROM SX_CONVERSATION_HISTORY 
WHERE USER_ID = 'jiliang' AND IS_DELETED = '0'
GROUP BY THREAD_ID
ORDER BY last_round_time DESC;

-- 3. 获取指定对话的最新几轮
SELECT ROUND_NUMBER, CONVERSATION_JSON, CREATED_AT
FROM SX_CONVERSATION_HISTORY 
WHERE THREAD_ID = 'jiliang--abcde' AND IS_DELETED = '0'
ORDER BY ROUND_NUMBER DESC
LIMIT 5;

-- 4. 统计用户的对话数据
SELECT COUNT(DISTINCT THREAD_ID) as total_conversations,
       COUNT(*) as total_rounds,
       SUM(MESSAGE_COUNT) as total_messages,
       SUM(TOKEN_COUNT) as total_tokens
FROM SX_CONVERSATION_HISTORY 
WHERE USER_ID = 'jiliang' AND IS_DELETED = '0';

-- 5. 查询最活跃的agent
SELECT PRIMARY_AGENT, COUNT(*) as round_count, SUM(TOKEN_COUNT) as total_tokens
FROM SX_CONVERSATION_HISTORY 
WHERE IS_DELETED = '0' AND STATUS = 'COMPLETED'
GROUP BY PRIMARY_AGENT
ORDER BY round_count DESC;

-- 6. 按时间范围查询对话
SELECT THREAD_ID, COUNT(*) as rounds, SUM(MESSAGE_COUNT) as messages
FROM SX_CONVERSATION_HISTORY 
WHERE USER_ID = 'jiliang' 
  AND IS_DELETED = '0'
  AND CREATED_AT BETWEEN '2025-08-01' AND '2025-08-31'
GROUP BY THREAD_ID
ORDER BY MAX(CREATED_AT) DESC;
*/
